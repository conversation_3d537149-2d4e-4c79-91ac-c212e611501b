SELECT TOP 1000
       [assessment].[AssessmentId]
      ,[assessment].[JobId]
      ,[job].[JobReference] [__Job]
      ,[assessment].[StatusCode]
      ,[status].[Description] [__Status]
      ,[assessment].[NCCClimateZoneCode]
      ,[nccClimateZone].[Description] [__NCCClimateZone]
      ,[assessment].[NatHERSClimateZoneCode]
      ,[natHERSClimateZone].[Description] [__NatHERSClimateZone]
      ,[assessment].[BuildingExposureCode]
      ,[buildingExposure].[Description] [__BuildingExposure]
      ,[assessment].[PerformanceRequirementP261Code]
      ,[assessment].[PerformanceRequirementP262Code]
      ,[assessment].[ComplianceStatusCode]
      ,[complianceStatus].[Description] [__ComplianceStatus]
      ,[assessment].[CertificateNumber]
      ,[assessment].[CerficateDate]
      ,[assessment].[AssessorSignatureFile]
      ,[assessment].[CreatedOn]
      ,[assessment].[CreatedByName]
      ,[assessment].[ModifiedOn]
      ,[assessment].[ModifiedByName]
      ,[assessment].[Deleted]
      ,[assessment].[BushfireAttackLevelCode]
      ,[bushfireAttackLevel].[Description] [__BushfireAttackLevel]
      ,[assessment].[Notes]
      ,[assessment].[AssessmentTemplateId]
      ,[assessment].[PriorityCode]
      ,[priority].[Description] [__Priority]
      ,[assessment].[AssessmentSoftwareOther]
      ,[assessment].[SummaryReport]

      ,[assessment].[IncludeMapInExportedPDF]
      ,[assessment].[IsBushFireProne]
      ,[assessment].[BushFireProneUnknown]
      ,[assessment].[AssessorUserId]
      ,[assessor].[FullName] [__Assessor]
      ,[assessment].[WorksDescriptionCode]
      ,[worksDescription].[Description] [__WorksDescription]
      ,[assessment].[WorksDescriptionOther]
      ,[assessment].[AssessorNotesNotApplicable]
      ,[assessment].[AssessorNotes]
      ,[assessment].[RecertificationDesignChangesJson]
      ,[assessment].[ComfortMetricsJson]
      ,[assessment].[LightAndVentilationCertificateNumber]
      ,[assessment].[CertificateDateOverride]
  FROM [dbo].[RSS_Assessment] [assessment]
  INNER JOIN [dbo].[RSS_Job] [job] ON [assessment].[JobId] = [job].[JobId]
  INNER JOIN [dbo].[RSS_Client] [client] ON [job].[ClientId] = [client].[ClientId]
  LEFT JOIN [dbo].[RSS_Status] [status] ON [assessment].[StatusCode] = [status].[StatusCode]
  LEFT JOIN [dbo].[RSS_NCCClimateZone] [nccClimateZone] ON [assessment].[NCCClimateZoneCode] = [nccClimateZone].[NCCClimateZoneCode]
  LEFT JOIN [dbo].[RSS_NatHERSClimateZone] [natHERSClimateZone] ON [assessment].[NatHERSClimateZoneCode] = [natHERSClimateZone].[NatHERSClimateZoneCode]
  LEFT JOIN [dbo].[RSS_BuildingExposure] [buildingExposure] ON [assessment].[BuildingExposureCode] = [buildingExposure].[BuildingExposureCode]
  LEFT JOIN [dbo].[RSS_ComplianceStatus] [complianceStatus] ON [assessment].[ComplianceStatusCode] = [complianceStatus].[ComplianceStatusCode]
  LEFT JOIN [dbo].[RSS_BushfireAttackLevel] [bushfireAttackLevel] ON [assessment].[BushfireAttackLevelCode] = [bushfireAttackLevel].[BushfireAttackLevelCode]
  LEFT JOIN [dbo].[RSS_Priority] [priority] ON [assessment].[PriorityCode] = [priority].[PriorityCode]
  LEFT JOIN [dbo].[RSS_User] [assessor] ON [assessment].[AssessorUserId] = [assessor].[UserId]
  LEFT JOIN [dbo].[RSS_WorksDescription] [worksDescription] ON [assessment].[WorksDescriptionCode] = [worksDescription].[WorksDescriptionCode]
  WHERE 1=1
    -- AND [AssessmentId] = 'a99cf3b8-ef49-482f-8d82-e9692b70ee8c'
    -- AND [client].[ClientId] = '078de7b3-f16e-4201-8ea1-7560dd6766f7'
  ORDER BY [assessment].[CreatedOn] DESC


  -- Set Assessment to "Complete"
--   UPDATE [dbo].[RSS_Assessment]
--   SET [StatusCode] = 'AIssued'
--   WHERE [AssessmentId] = 'a99cf3b8-ef49-482f-8d82-e9692b70ee8c'

  -- Set Assessment to "Compliance Options Provided"
--   UPDATE [dbo].[RSS_Assessment]
--   SET [StatusCode] = 'ACompliance'
--   WHERE [AssessmentId] = '3c11f3da-7257-4aac-8e1a-e191565e233d' -- Test2 J1919
--   WHERE [AssessmentId] = '0e83573a-1bbf-40da-97b8-4ee93349ff94' -- Test2 J2219
--   WHERE [AssessmentId] = '3cddb5e9-9936-4af3-ba2d-45ed3f7dd29c' -- Test2 J2213
--   WHERE [AssessmentId] = '5cabb168-ce65-457e-a1a5-48fc15245a3a' -- Test2 J2217
--   WHERE [AssessmentId] = '68d60df4-7202-4828-b956-fcb7d5e60ef2'

--   UPDATE [dbo].[RSS_Assessment]
-- --   SET [NCC2022Json] = NULL
--   SET [Deleted] = 1
--   WHERE [AssessmentId] = '12c9f1c0-5560-4348-80bf-f45f5f51b7e1'