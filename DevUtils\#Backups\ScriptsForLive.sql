-- =============================================
-- Scripts For Live Database Migration Script
-- This script performs database migration operations for the live environment
--
-- IMPORTANT: This script runs in separate transactions - if ANY part fails,
-- the entire script will be rolled back to maintain database consistency
-- =============================================

SET NOCOUNT ON
SET XACT_ABORT ON  -- Automatically rollback on any error

-- =============================================
-- TRANSACTION 1: CREATE TABLE and ALTER TABLE statements
-- =============================================
BEGIN TRANSACTION ScriptsForLive_DDL

BEGIN TRY
    PRINT 'Starting Scripts For Live database migration - DDL operations...'


-- Undo creating Identity tables

IF OBJECT_ID('FK_RSS_UsersClient_AspNetUsers') IS NOT NULL BEGIN
    ALTER TABLE [dbo].[RSS_UsersClient] DROP CONSTRAINT [FK_RSS_UsersClient_AspNetUsers];
END
IF OBJECT_ID('FK_RSS_User_AspNetUsers') IS NOT NULL BEGIN
    ALTER TABLE [dbo].[RSS_User] DROP CONSTRAINT [FK_RSS_User_AspNetUsers];
END
IF OBJECT_ID('FK_RSS_RolesBusinessRole_AspNetRoles') IS NOT NULL BEGIN
    ALTER TABLE [dbo].[RSS_RolesBusinessRole] DROP CONSTRAINT [FK_RSS_RolesBusinessRole_AspNetRoles];
END

IF OBJECT_ID('AspNetUserRoles', 'U') IS NOT NULL BEGIN
    DROP TABLE [dbo].[AspNetUserRoles];
END
IF OBJECT_ID('AspNetUserClaims', 'U') IS NOT NULL BEGIN
    DROP TABLE [dbo].[AspNetUserClaims];
END
IF OBJECT_ID('AspNetUserLogins', 'U') IS NOT NULL BEGIN
    DROP TABLE [dbo].[AspNetUserLogins];
END
IF OBJECT_ID('AspNetRoles', 'U') IS NOT NULL BEGIN
    DROP TABLE [dbo].[AspNetRoles];
END
IF OBJECT_ID('AspNetUsers', 'U') IS NOT NULL BEGIN
    DROP TABLE [dbo].[AspNetUsers];
END
IF OBJECT_ID('vw_AspNetUserRoles') IS NOT NULL BEGIN
    DROP VIEW [dbo].[vw_AspNetUserRoles];
END

-- Create AspNetUsers
CREATE TABLE [dbo].[AspNetUsers] (
    [Id]                                     NVARCHAR(128)    NOT NULL,
    [UserName]                               NVARCHAR(MAX)    NULL,
    [PasswordHash]                           NVARCHAR(MAX)    NULL,
    [SecurityStamp]                          NVARCHAR(MAX)    NULL,
    [EmailConfirmed]                         BIT              NOT NULL,
    [PhoneNumber]                            NVARCHAR(MAX)    NULL,
    [PhoneNumberConfirmed]                   BIT              NOT NULL,
    [TwoFactorEnabled]                       BIT              NOT NULL,
    [LockoutEndDateUtc]                      DATETIME         NULL,
    [LockoutEnabled]                         BIT              NOT NULL,
    [AccessFailedCount]                      INT              NOT NULL,
    [ApplicationId]                          UNIQUEIDENTIFIER NOT NULL,
    [LegacyPasswordHash]                     NVARCHAR(MAX)    NULL,
    [LoweredUserName]                        NVARCHAR(256)    NOT NULL,
    [MobileAlias]                            NVARCHAR(16)     NULL     DEFAULT (NULL),
    [IsAnonymous]                            BIT              NOT NULL DEFAULT (0),
    [LastActivityDate]                       DATETIME2        NOT NULL,
    [MobilePIN]                              NVARCHAR(16)     NULL,
    [Email]                                  NVARCHAR(256)    NULL,
    [LoweredEmail]                           NVARCHAR(256)    NULL,
    [PasswordQuestion]                       NVARCHAR(256)    NULL,
    [PasswordAnswer]                         NVARCHAR(128)    NULL,
    [IsApproved]                             BIT              NOT NULL,
    [IsLockedOut]                            BIT              NOT NULL,
    [CreateDate]                             DATETIME2        NOT NULL,
    [LastLoginDate]                          DATETIME2        NOT NULL,
    [LastPasswordChangedDate]                DATETIME2        NOT NULL,
    [LastLockoutDate]                        DATETIME2        NOT NULL,
    [FailedPasswordAttemptCount]             INT              NOT NULL,
    [FailedPasswordAttemptWindowStart]       DATETIME2        NOT NULL,
    [FailedPasswordAnswerAttemptCount]       INT              NOT NULL,
    [FailedPasswordAnswerAttemptWindowStart] DATETIME2        NOT NULL,
    [Comment]                                NTEXT            NULL,
    [LastTwoFacAuthDate]                     DATETIME         NULL,
    [TwoFacAuthSecret]                       VARBINARY(128)   NULL,
    CONSTRAINT [PK_dbo.AspNetUsers] PRIMARY KEY CLUSTERED ([Id] ASC),
    FOREIGN KEY ([ApplicationId]) REFERENCES [dbo].[aspnet_Applications] ([ApplicationId]),
)

    PRINT 'DDL operations completed successfully.'
    COMMIT TRANSACTION ScriptsForLive_DDL
END TRY
BEGIN CATCH
    PRINT 'Error occurred during DDL operations: ' + ERROR_MESSAGE()
    ROLLBACK TRANSACTION ScriptsForLive_DDL

    -- Re-raise the error using RAISERROR for compatibility
    DECLARE @DDLErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
    DECLARE @DDLErrorSeverity INT = ERROR_SEVERITY()
    DECLARE @DDLErrorState INT = ERROR_STATE()
    RAISERROR(@DDLErrorMessage, @DDLErrorSeverity, @DDLErrorState)
END CATCH

-- =============================================
-- TRANSACTION 2: INSERT statements
-- =============================================
BEGIN TRANSACTION ScriptsForLive_DML

BEGIN TRY
    PRINT 'Starting Scripts For Live database migration - DML operations...'

-- Create AspNetRoles
CREATE TABLE [dbo].[AspNetRoles] (
    [Id]            NVARCHAR(128) NOT NULL,
    [Name]          NVARCHAR(MAX) NOT NULL,
	[Discriminator] NVARCHAR(128) NULL,
	[Description]   NVARCHAR(max) NULL,
    PRIMARY KEY NONCLUSTERED ([Id] ASC),
)

-- Create AspNetUserRoles
CREATE TABLE [dbo].[AspNetUserRoles] (
    [UserId] NVARCHAR(128) NOT NULL,
    [RoleId] NVARCHAR(128) NOT NULL,
    CONSTRAINT [PK_dbo.AspNetUserRoles] PRIMARY KEY CLUSTERED ([UserId] ASC, [RoleId] ASC),
    CONSTRAINT [FK_dbo.AspNetUserRoles_dbo.AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[AspNetRoles] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_dbo.AspNetUserRoles_dbo.AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [dbo].[AspNetUsers] ([Id]) ON DELETE CASCADE
)

-- Create AspNetUserClaims
CREATE TABLE [dbo].[AspNetUserClaims] (
    [Id]         INT IDENTITY (1, 1) NOT NULL,
    [ClaimType]  NVARCHAR(MAX)      NULL,
    [ClaimValue] NVARCHAR(MAX)      NULL,
    [UserId]     NVARCHAR(128)      NOT NULL,
    CONSTRAINT [PK_dbo.AspNetUserClaims] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_dbo.AspNetUserClaims_dbo.AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [dbo].[AspNetUsers] ([Id]) ON DELETE CASCADE
)
CREATE NONCLUSTERED INDEX [IX_UserId] ON [dbo].[AspNetUserClaims]([UserId] ASC)

-- Create AspNetUserLogins
CREATE TABLE [dbo].[AspNetUserLogins] (
    [UserId]        NVARCHAR(128) NOT NULL,
    [LoginProvider] NVARCHAR(128) NOT NULL,
    [ProviderKey]   NVARCHAR(128) NOT NULL,
    CONSTRAINT [PK_dbo.AspNetUserLogins] PRIMARY KEY CLUSTERED ([UserId] ASC, [LoginProvider] ASC, [ProviderKey] ASC),
    CONSTRAINT [FK_dbo.AspNetUserLogins_dbo.AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [dbo].[AspNetUsers] ([Id]) ON DELETE CASCADE
)
CREATE NONCLUSTERED INDEX [IX_UserId] ON [dbo].[AspNetUserLogins]([UserId] ASC)

    PRINT 'DDL operations completed successfully.'
    COMMIT TRANSACTION ScriptsForLive_DDL
END TRY
BEGIN CATCH
    PRINT 'Error occurred during DDL operations: ' + ERROR_MESSAGE()
    ROLLBACK TRANSACTION ScriptsForLive_DDL

    -- Re-raise the error using RAISERROR for compatibility
    DECLARE @DDL2ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
    DECLARE @DDL2ErrorSeverity INT = ERROR_SEVERITY()
    DECLARE @DDL2ErrorState INT = ERROR_STATE()
    RAISERROR(@DDL2ErrorMessage, @DDL2ErrorSeverity, @DDL2ErrorState)
END CATCH

-- =============================================
-- TRANSACTION 2: INSERT statements
-- =============================================
BEGIN TRANSACTION ScriptsForLive_DML

BEGIN TRY
    PRINT 'Starting Scripts For Live database migration - DML operations...'

-- Views will be created at the end of the script

CREATE TABLE [dbo].[RSS_Contact](
	[ContactId] [uniqueidentifier] NOT NULL,
	[FullName] [varchar](100) NOT NULL,
	[FirstName] [varchar](60) NOT NULL,
	[LastName] [varchar](60) NULL,
	[Phone] [varchar](20) NULL,
	[Fax] [varchar](20) NULL,
	[MobilePhone] [varchar](20) NULL,
	[EmailAddress] [varchar](150) NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedByName] [varchar](50) NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedByName] [varchar](50) NULL,
	[Deleted] [bit] NOT NULL DEFAULT((0)),
    [ClientId] [uniqueidentifier] NOT NULL, -- FK to RSS_Client
CONSTRAINT [PK_RSS_Contact] PRIMARY KEY CLUSTERED
(
	[ContactId] DESC
) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]


-- Foreign key for RSS_AssessmentDrawing.AssessmentId
DELETE FROM RSS_AssessmentDrawing
WHERE NOT EXISTS (
    SELECT 1 
    FROM RSS_Assessment 
    WHERE RSS_Assessment.AssessmentId = RSS_AssessmentDrawing.AssessmentId
);
ALTER TABLE [dbo].[RSS_AssessmentDrawing]  WITH CHECK ADD  CONSTRAINT [FK_RSS_AssessmentDrawing_RSS_Assessment] FOREIGN KEY([AssessmentId])
REFERENCES [dbo].[RSS_Assessment] ([AssessmentId])
ON DELETE CASCADE
ALTER TABLE [dbo].[RSS_AssessmentDrawing] CHECK CONSTRAINT [FK_RSS_AssessmentDrawing_RSS_Assessment]

--changeset SG:4
CREATE TABLE [dbo].[RSS_NCCColourClassification](
    [nccColourClassificationCode] [varchar](20) NOT NULL,
    [Description] [varchar](100) NULL,
    
	[CreatedOn] [datetime] NOT NULL,
	[CreatedByName] [varchar](50) NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedByName] [varchar](50) NULL,
	[Deleted] [bit] NOT NULL DEFAULT((0)),
CONSTRAINT [PK_RSS_NCCColourClassification] PRIMARY KEY CLUSTERED
(
	[nccColourClassificationCode] DESC
) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]


--changeset JCC:001
CREATE TABLE [dbo].[RSS_ProjectType]
(
   [ProjectTypeCode] VARCHAR(50)  NOT NULL CONSTRAINT [PK_RSS_ProjectType]           PRIMARY KEY,
   [Description]     VARCHAR(200) NOT NULL,
   [SortOrder]       SMALLINT     NOT NULL CONSTRAINT [DF_RSS_ProjectType_SortOrder] DEFAULT 999,
   [Deleted]         BIT          NOT NULL CONSTRAINT [DF_RSS_ProjectType_Deleted]   DEFAULT 0
)

CREATE TABLE [dbo].[RSS_Project]
(
   [ProjectId]              UNIQUEIDENTIFIER NOT NULL CONSTRAINT [PK_RSS_Project]                        PRIMARY KEY,
   [ClientId]               UNIQUEIDENTIFIER     NULL CONSTRAINT [FK_RSS_Project_RSS_Client]             REFERENCES [dbo].[RSS_Client] ([ClientId]),
   [ProjectName]            VARCHAR(100)     NOT NULL,
   [Description]            VARCHAR(500)         NULL,
   [ProjectTypeCode]        VARCHAR(50)               CONSTRAINT [FK_RSS_Project_RSS_ProjectType]        REFERENCES [dbo].[RSS_ProjectType] ([ProjectTypeCode]),
   [IsActive]               BIT              NOT NULL CONSTRAINT [DF_RSS_Project_Active]                 DEFAULT 1,
   [Lots]                   INT                  NULL,
   [LotArea]                DECIMAL(25,5)        NULL,
   [LogoFileId]             UNIQUEIDENTIFIER     NULL CONSTRAINT [FK_RSS_Project_RSS_File]               REFERENCES [dbo].[RSS_File]([FileId]),
   [SuburbCode]             UNIQUEIDENTIFIER     NULL CONSTRAINT [FK_RSS_Project_RSS_Suburb]             REFERENCES [dbo].[RSS_Suburb]([SuburbCode]),
   [StateCode]              VARCHAR(20)          NULL CONSTRAINT [FK_RSS_Project_RSS_State]              REFERENCES [dbo].[RSS_State]([StateCode]),
   [LGA]                    VARCHAR(100)         NULL,
   [NatHERSClimateZoneCode] VARCHAR(20)          NULL CONSTRAINT [FK_RSS_Project_RSS_NatHERSClimateZone] REFERENCES [dbo].[RSS_NatHERSClimateZone] ([NatHERSClimateZoneCode]),
   [NCCClimateZoneCode]     VARCHAR(20)          NULL CONSTRAINT [FK_RSS_Project_RSS_NCCClimateZone]     REFERENCES [dbo].[RSS_NCCClimateZone] ([NCCClimateZoneCode]),
   [Latitude]               DECIMAL(13, 9)       NULL,
   [Longitude]              DECIMAL(13, 9)       NULL,
   [LockWOHLocation]        BIT              NOT NULL CONSTRAINT [DF_RSS_Project_LockWOHLocation]        DEFAULT 0,
   [EnergyLabsSettingsJson] NVARCHAR(max)        NULL,
   [CreatedOn]              DATETIME         NOT NULL CONSTRAINT [DF_RSS_Project_CreatedOn]              DEFAULT GETDATE(),
   [CreatedByName]          VARCHAR(50)          NULL CONSTRAINT [DF_RSS_Project_CreatedByName]          DEFAULT 'System',
   [ModifiedOn]             DATETIME             NULL,
   [ModifiedByName]         VARCHAR(50)          NULL,
   [Deleted]                BIT              NOT NULL CONSTRAINT [DF_RSS_Project_Deleted]                DEFAULT 0
)

CREATE TABLE [dbo].[RSS_UserProject]
(
   [UserProjectId]  UNIQUEIDENTIFIER NOT NULL CONSTRAINT [PK_RSS_UserProject]               PRIMARY KEY,
   [UserId]         UNIQUEIDENTIFIER NOT NULL CONSTRAINT [FK_RSS_UserProject_RSS_User]      REFERENCES [dbo].[RSS_User] ([UserId]),
   [ProjectId]      UNIQUEIDENTIFIER NOT NULL CONSTRAINT [FK_RSS_UserProject_RSS_Project]   REFERENCES [dbo].[RSS_Project] ([ProjectId]),
   [CreatedOn]      DATETIME         NOT NULL CONSTRAINT [DF_RSS_UserProject_CreatedOn]     DEFAULT GETDATE(),
   [CreatedByName]  VARCHAR(50)          NULL CONSTRAINT [DF_RSS_UserProject_CreatedByName] DEFAULT 'System',
   [ModifiedOn]     DATETIME             NULL,
   [ModifiedByName] VARCHAR(50)          NULL,
   [Deleted]        BIT              NOT NULL CONSTRAINT [DF_RSS_UserProject_Deleted]       DEFAULT 0
)

--changeset JCC:002
CREATE TABLE [dbo].[RSS_WaDesignCode] (
    WaDesignCodeCode VARCHAR(50) NOT NULL PRIMARY KEY,
    Title NVARCHAR(50),
    SortOrder SMALLINT NOT NULL,
    Deleted BIT CONSTRAINT DF_RSS_WaDesignCode_Deleted DEFAULT 0 NOT NULL,
    ForDesignCodeCategory VARCHAR(50)
)

--changeset JCC:003
CREATE TABLE [dbo].[RSS_BushfireStateData] (
    [StateCode]       VARCHAR(20) NOT NULL CONSTRAINT [PK_BushfireStateData] PRIMARY KEY
                                           CONSTRAINT [FK_RSS_BushfireStateData_RSS_State] REFERENCES [dbo].[RSS_State] ([StateCode]),
    [HasBushfireData] BIT         NOT NULL CONSTRAINT [DF_BushfireStateData] DEFAULT 0,
    [ModifiedOn]      DATETIME    NULL,
    [ModifiedByName]  VARCHAR(50) NULL
)

--changeset JCC:050325
    -- [THR-672]
    -- User Permissions
CREATE TABLE [dbo].[RSS_RolesUserSelectionsData] (
    [UserId] NVARCHAR(128) NOT NULL CONSTRAINT [FK_RSS_RolesUserSelectionsData_AspNetUsers] FOREIGN KEY REFERENCES [dbo].[AspNetUsers] ([Id]),
    [RoleId] NVARCHAR(128) NOT NULL CONSTRAINT [FK_RSS_RolesUserSelectionsData_AspNetRoles] FOREIGN KEY REFERENCES [dbo].[AspNetRoles] ([Id]),
    [SelectionsJson] VARCHAR(MAX),
    CONSTRAINT [PK_RSS_RolesUserSelectionsData] PRIMARY KEY CLUSTERED 
    (
	    [UserId] ASC,
	    [RoleId] ASC
    ) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


--changeset JCC:001
INSERT INTO [dbo].[RSS_ProjectType]
([ProjectTypeCode], [Description], [SortOrder])
VALUES
('Standard', 'Standard Home Designs', 1),
('Development', 'Land Development', 2),
('Custom Project', 'Custom Project', 3)

--changeset JCC:002
INSERT INTO [dbo].[RSS_WaDesignCode]
([WaDesignCodeCode], [Title], [SortOrder], [ForDesignCodeCategory])
VALUES
('Concealed', 'Concealed Roof', 10, 'RoofType'),
('Gabled', 'Gabled Roof', 20, 'RoofType'),
('Hipped', 'Hipped Roof', 30, 'RoofType'),
('Pitched', 'Pitched Roof', 40, 'RoofType'),
('Skillion', 'Skillion Roof', 50, 'RoofType'),
('Ground', 'Ground Floor', 60, 'LocationPrimaryLivingSpace'),
('Upper', 'Upper Floor', 70, 'LocationPrimaryLivingSpace'),
('0', '0', 90, 'NorthOffset'),
('45', '45', 100, 'NorthOffset'),
('90', '90', 110, 'NorthOffset'),
('135', '135', 120, 'NorthOffset'),
('180', '180', 130, 'NorthOffset'),
('225', '225', 140, 'NorthOffset'),
('270', '270', 150, 'NorthOffset'),
('315', '315', 160, 'NorthOffset'),
('No', 'No', 170, 'StorageArea'),
('external', 'External Access', 180, 'StorageArea'),
('garage', 'Garage Access', 190, 'StorageArea')

--changeset JCC:003
UPDATE [dbo].[RSS_ServiceCategory] SET [Title] = 'Heating System' WHERE [ServiceCategoryCode] = 'SpaceHeatingSystem'
UPDATE [dbo].[RSS_ServiceCategory] SET [Title] = 'Cooling System' WHERE [ServiceCategoryCode] = 'SpaceCoolingSystem'

--changeset JCC:004
UPDATE [dbo].[RSS_ServiceFuelType] SET [Title] = 'Electric' WHERE [ServiceFuelTypeCode] = 'Electricity'

--changeset JCC:005
INSERT INTO [dbo].[RSS_SectorDetermination]
([SectorDeterminationCode], [Title], [SectorsJSON], [SortOrder])
VALUES
('NatHERS', 'NatHERS (16 Sectors)', '
    [
      { "operator1": ">", "min": 348.75, "operator2": "<=", "max": 360,    "label": "N",   "description": "North"            },
      { "operator1": ">", "min": 0,      "operator2": "<=", "max": 11.25,  "label": "N",   "description": "North"            },
      { "operator1": ">", "min": 11.25,  "operator2": "<=", "max": 33.75,  "label": "NNE", "description": "North North-East" },
      { "operator1": ">", "min": 33.75,  "operator2": "<=", "max": 56.25,  "label": "NE",  "description": "North-East"       },
      { "operator1": ">", "min": 56.25,  "operator2": "<=", "max": 78.75,  "label": "ENE", "description": "East North-East"  },
      { "operator1": ">", "min": 78.75,  "operator2": "<=", "max": 101.25, "label": "E",   "description": "East"             },
      { "operator1": ">", "min": 101.25, "operator2": "<=", "max": 123.75, "label": "ESE", "description": "East South-East"  },
      { "operator1": ">", "min": 123.75, "operator2": "<=", "max": 146.25, "label": "SE",  "description": "South-East"       },
      { "operator1": ">", "min": 146.25, "operator2": "<=", "max": 168.75, "label": "SSE", "description": "South South-East" },
      { "operator1": ">", "min": 168.75, "operator2": "<=", "max": 191.25, "label": "S",   "description": "South"            },
      { "operator1": ">", "min": 191.25, "operator2": "<=", "max": 213.75, "label": "SSW", "description": "South-South-West" },
      { "operator1": ">", "min": 213.75, "operator2": "<=", "max": 236.25, "label": "SW",  "description": "South-West"       },
      { "operator1": ">", "min": 236.25, "operator2": "<=", "max": 258.75, "label": "WSW", "description": "West South-West"  },
      { "operator1": ">", "min": 258.75, "operator2": "<=", "max": 281.25, "label": "W",   "description": "West"             },
      { "operator1": ">", "min": 281.25, "operator2": "<=", "max": 303.75, "label": "WNW", "description": "West North-West"  },
      { "operator1": ">", "min": 303.75, "operator2": "<=", "max": 326.25, "label": "NW",  "description": "North-West"       },
      { "operator1": ">", "min": 326.25, "operator2": "<=", "max": 348.75, "label": "NNW", "description": "North North-West" }
    ]
', 5000)

--changeset JCC:006
INSERT INTO [dbo].[aspnet_Roles]
([ApplicationId],                        [RoleId],                                [RoleName],             [LoweredRoleName],         [Description])
VALUES
('D8C8B73E-45BB-4C48-90BA-DD37DD5C3A4B', '621D146F-9109-4877-BDBE-37B656E4DA0F',  'ComplianceCostAccess', 'compliancecostaccess',    'Compliance Cost Access')

--changeset JCC:007
DELETE [dbo].[aspnet_Roles] WHERE [RoleName] = 'ComplianceCostAccess'

INSERT INTO [dbo].[aspnet_Roles]
([ApplicationId],                        [RoleId],                                [RoleName],           [LoweredRoleName],       [Description])
VALUES
('D8C8B73E-45BB-4C48-90BA-DD37DD5C3A4B', 'e9a75aaf-2ec3-4469-b003-2960f9f43be1',  'EditComplianceCost', 'editcompliancecost',    'Edit Compliance Cost'),
('D8C8B73E-45BB-4C48-90BA-DD37DD5C3A4B', '6b656f09-a1f6-4020-a0ae-c047ac87dc84',  'ViewComplianceCost', 'viewcompliancecost',    'View Compliance Cost')

--changeset JCC:008
INSERT INTO [dbo].[RSS_BushfireStateData]
([StateCode], [HasBushfireData])
VALUES
('ACT', 0),
('NSW', 0),
('NT',  0),
('OT',  0),
('QLD', 0),
('SA',  0),
('SS',  0),
('TAS', 0),
('VIC', 0),
('WA',  1)

-- changeset JCC:041
INSERT [dbo].[RSS_SystemParameters]
([ParmCode],                            [Parmint], [ParmDesc],                                                                                                        [UserEditAllowed], [UserDisplay])
VALUES
('BypassMultiFilterOptionsCacheAdmin',  0,         'Sets to true when a Job/Assessment is updated, so Jobs lists on Admin force retrieves new values on next load.',  0,                 0),
('BypassFilterCountDataCacheAdmin',     0,         'Sets to true when a Job/Assessment is updated, so Jobs lists on Admin force retrieves new values on next load.',  0,                 0),
('BypassMultiFilterOptionsCacheClient', 0,         'Sets to true when a Job/Assessment is updated, so Jobs lists on Client force retrieves new values on next load.', 0,                 0),
('BypassFilterCountDataCacheClient',    0,         'Sets to true when a Job/Assessment is updated, so Jobs lists on Client force retrieves new values on next load.', 0,                 0)


--changeset JCC:042
INSERT [dbo].[RSS_SystemParameters]
([ParmCode],              [ParmString], [ParmDesc],                                             [UserEditAllowed], [UserDisplay])
VALUES
('FloorHeightTolerance',  0.3,          'Used in Scratch processing to determin the Storeys.',  0,                 0)

--changeset JCC:043
    -- [THR-547] Save global Whole-of-Home Calculator defaults
INSERT [dbo].[RSS_SystemParameters]
([ParmCode],                       [ParmString], [ParmDesc],                                                                                                     [UserEditAllowed], [UserDisplay])
VALUES
('WholeOfHomeCalculatorDefaults',  '{}',         'Used on WHole-of-Home Calculator modal as initial values when access outisde of Assessment on Admin portal.',  0,                 0)



--changeset JCC:046
INSERT [dbo].[RSS_SystemParameters]
([ParmCode],            [ParmInt], [ParmDesc],                                    [UserEditAllowed], [UserDisplay])
VALUES
('SlipBypassDownload',  0,         'Allow force slip processing now on startup.', 0,                 0)



--changeset LJT:188
-- Notification recipients, rules and templates for assessment notification functionality.
CREATE TABLE dbo.RSS_NotificationRecipientType
(
    NotificationRecipientTypeCode varchar(40) PRIMARY KEY NOT NULL,
    Title                             varchar(100)            NOT NULL,
    [Description]                     varchar(500)            NULL,

    Deleted        bit         NOT NULL DEFAULT (0),
    SortOrder      smallint    NOT NULL,
    CreatedOn      datetime    NOT NULL DEFAULT (CURRENT_TIMESTAMP),
    CreatedByName  varchar(50) NOT NULL DEFAULT ('System'),
    ModifiedOn     datetime    NULL,
    ModifiedByName varchar(50) NULL,
)

INSERT INTO dbo.RSS_NotificationRecipientType (NotificationRecipientTypeCode, Title, [Description], SortOrder)
VALUES ('Admin', 'Admin', 'The special Thermarate administrator account.', 10),
       ('Assessor', 'Assessor', 'The Thermarate Assessor assigned to the assessment.', 20),
       ('Assignee', 'Assignee', 'The external (client) user assigned to the assessment.', 30)

CREATE TABLE dbo.RSS_NotificationRule (

                                          NotificationRuleId uniqueidentifier PRIMARY KEY NOT NULL,

                                          Title                  varchar(100)                 NOT NULL,
                                          [Description]          varchar(500)                 NULL,

    -- Initial status code required to trigger rule. If NULL, indicates there is NO initial status required, simply
    -- having the 'New' status code be meet the requirement AND be different from the initial status code.
                                          InitialStatusCode varchar(20)     NULL
                                              CONSTRAINT FK_RSS_RSS_NotificationRule_InitialStatusCode REFERENCES dbo.RSS_Status,

    -- Status code requirement at the end of the update/save logic.
                                          NewStatusCode     varchar(20) NOT NULL
                                              CONSTRAINT FK_RSS_RSS_NotificationRule_NewStatusCode REFERENCES dbo.RSS_Status,

                                          Enabled bit NOT NULL CONSTRAINT DF_RSS_NotificationRule_Enabled DEFAULT(1),

                                          Deleted        bit         NOT NULL DEFAULT (0),
                                          SortOrder      smallint    NOT NULL,
                                          CreatedOn      datetime    NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                                          CreatedByName  varchar(50) NOT NULL DEFAULT ('System'),
                                          ModifiedOn     datetime    NULL,
                                          ModifiedByName varchar(50) NULL,

)

CREATE TABLE dbo.RSS_NotificationTemplate (

                                              NotificationTemplateId uniqueidentifier NOT NULL PRIMARY KEY,

    -- Rule this template is linked to
                                              NotificationRuleId uniqueidentifier NOT NULL
                                                  CONSTRAINT FK_RSS_NotificationRuleRecipientType_NotificationRuleId
                                                      REFERENCES dbo.RSS_NotificationRule,

    -- Recipient this template should be sent to when rule is matched.
                                              NotificationRecipientTypeCode varchar(40) NOT NULL
                                                  CONSTRAINT FK_RSS_NotificationRuleRecipientType_RecipientCode
                                                      REFERENCES RSS_NotificationRecipientType,

                                              [Subject]              varchar(200)                 NOT NULL,
                                              EmailPlainText      varchar(max)                 NOT NULL,
                                              EmailHtml           varchar(max)                 NOT NULL,

                                              Deleted        bit         NOT NULL DEFAULT (0),
                                              CreatedOn      datetime    NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                                              CreatedByName  varchar(50) NOT NULL DEFAULT ('System'),
                                              ModifiedOn     datetime    NULL,
                                              ModifiedByName varchar(50) NULL,
)

-- Dispatch request queue takes care of Assessments which can very quickly have their status updated many times in less
-- than a minute. To remove 'duplicate' (or simply 'mass') emails being sent, there is a delay of ~n minutes before
-- emails are actually sent. Every update resets the counter. If an assessment gets its 'new status' changed back to
-- its 'initial status' when the counter finally hits the required timer mark, no notification is sent.
CREATE TABLE dbo.RSS_NotificationDispatchCheckRequest
(

    NotificationDispatchCheckRequestId uniqueidentifier NOT NULL PRIMARY KEY,
    AssessmentId                       uniqueidentifier NOT NULL
        CONSTRAINT FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId
            REFERENCES dbo.RSS_Assessment (AssessmentId),

    InitialStatusCode                  varchar(20)      NOT NULL
        CONSTRAINT FK_RSS_NotificationDispatchCheckRequest_InitialStatusCode REFERENCES dbo.RSS_Status,

    -- Status code at the end of the delay period.
    NewStatusCode                      varchar(20)      NOT NULL
        CONSTRAINT FK_RSS_NotificationDispatchCheckRequest_NewStatusCode REFERENCES dbo.RSS_Status,

    -- Time at which a rule should be checked and (if the rule meets criteria) notifications sent off.
    -- Should be reset whenever 'new status code' changes  within the delay time period.
    SendAfterUtc                       datetime         NOT NULL,
    [Status]                           nvarchar(30)     NOT NULL
        CONSTRAINT CHK_RSS_NotificationDispatchCheckRequest_Status
            CHECK ([Status] IN (
                                'pending', -- Request has not met the 'cooldown' period yet, so unchecked.
                                'checked'  -- Request was checked as it met the 'cooldown' period.
--                                'checked_reverted',  -- Request was checked, status had reverted to initial so not sent.
--                                'checked_not_req',   -- Request was checked, but no notification rule matched.
--                                'checked_sent',      -- Request was checked, and 1+ rules matched and sent.
--                                'checked_failed_send'-- Request checked, rule/s matched, but failed to send. Will retry?
                )),

    Deleted                            bit              NOT NULL DEFAULT (0),
    CreatedOn                          datetime         NOT NULL DEFAULT (CURRENT_TIMESTAMP),
    CreatedByName                      varchar(50)      NOT NULL DEFAULT ('System'),
    ModifiedOn                         datetime         NULL,
    ModifiedByName                     varchar(50)      NULL,

)

-- Re-order these so they make a bit more sense.
UPDATE dbo.RSS_Status SET SortOrder =  10 WHERE StatusCode = 'ADraft'
UPDATE dbo.RSS_Status SET SortOrder =  20 WHERE StatusCode = 'AInProgress'
UPDATE dbo.RSS_Status SET SortOrder =  30 WHERE StatusCode = 'APreliminaryReview'
UPDATE dbo.RSS_Status SET SortOrder =  40 WHERE StatusCode = 'ACompliance'
UPDATE dbo.RSS_Status SET SortOrder =  50 WHERE StatusCode = 'AOptionSelected'
UPDATE dbo.RSS_Status SET SortOrder =  60 WHERE StatusCode = 'AComplete'
UPDATE dbo.RSS_Status SET SortOrder =  70 WHERE StatusCode = 'AIssued'
UPDATE dbo.RSS_Status SET SortOrder =  80 WHERE StatusCode = 'ARecertification'
UPDATE dbo.RSS_Status SET SortOrder =  90 WHERE StatusCode = 'ACancelled'
UPDATE dbo.RSS_Status SET SortOrder = 100 WHERE StatusCode = 'ASuperseded'


--changeset LJT:189
-- ENERGY LABS STUFF
CREATE TABLE dbo.RSS_StandardHomeModel
(
    StandardHomeModelId uniqueidentifier NOT NULL PRIMARY KEY,

    ClientId            uniqueidentifier NULL
        CONSTRAINT FK_RSS_StandardHomeModel_Client_ClientID
            REFERENCES dbo.RSS_Client (ClientId),

    Title               varchar(100)     NOT NULL,
    [Description]       varchar(500)     NULL,
    Category            nvarchar(200),

    MinLotWidth         smallint         NULL,
    Width               smallint         NULL,
    FloorArea           smallint         NULL,
    Storeys             smallint         NULL,

    NumberOfBedrooms    smallint         NULL,
    NumberOfBathrooms   smallint         NULL,
    NumberOfGarageSpots smallint         NULL,

    NatHERSClimateZone  smallint         NULL,
    NorthOffset         smallint         NULL,
    BlockType           nvarchar(200)    NULL,

    VariableOptionsJson varchar(max)     NULL,

    HomePlanImageId     uniqueidentifier NULL
        CONSTRAINT FK_RSS_StandardHomeModel_RSS_FIle_FileId
            REFERENCES dbo.RSS_File (FileId),

    Deleted             bit              NOT NULL DEFAULT (0),
    CreatedOn           datetime         NOT NULL DEFAULT (CURRENT_TIMESTAMP),
    CreatedByName       varchar(50)      NOT NULL DEFAULT ('System'),
    ModifiedOn          datetime         NULL,
    ModifiedByName      varchar(50)      NULL,
)

CREATE TABLE dbo.RSS_StandardHomeModelOption
(
    StandardHomeModelOptionId            uniqueidentifier NOT NULL PRIMARY KEY,
    StandardHomeModelId                  uniqueidentifier NOT NULL
        CONSTRAINT FK_RSS_StandardHomeModelOption_StandardHomeModel_StandardHomeModelId
            REFERENCES dbo.RSS_StandardHomeModel (StandardHomeModelId),

    ClimateZone                          tinyint          NOT NULL,
    NorthOffset                          smallint         NOT NULL,
    BlockType                            varchar(100)     NOT NULL,
    RoofConstruction                     varchar(100)     NOT NULL,
    RoofInsulation                       varchar(100)     NOT NULL,
    RoofSolarAbsorptance                 decimal(12, 2)   NOT NULL,
    CeilingInsulation                    varchar(100)     NOT NULL,
    ExteriorWallConstruction             varchar(100)     NOT NULL,
    ExteriorWallInsulation               varchar(100)     NOT NULL,
    ExteriorWallSolarAbsorptance         decimal(12, 2)   NOT NULL,
    InteriorWallConstruction             varchar(100)     NOT NULL,
    InteriorWallInsulation               varchar(100)     NOT NULL,
    ExteriorGlazing                      varchar(100)     NOT NULL,
    ExteriorGlazingFrameSolarAbsorptance decimal(12, 2)   NOT NULL,
    FloorConstruction                    varchar(100)     NOT NULL,
    FloorInsulation                      varchar(100)     NOT NULL,
    FloorCoverings                       varchar(100)     NOT NULL,

    HeatingLoad                          decimal(12, 2)   NOT NULL,
    CoolingLoad                          decimal(12, 2)   NOT NULL,
    TotalEnergyLoad                      decimal(12, 2)   NOT NULL,
    EnergyRating                         decimal(12, 2)   NOT NULL,

    [Row]                                integer          NOT NULL,
)

--changeset JCC:180924.1
CREATE TABLE [dbo].[RSS_StandardHomeModelVariationOption] (
    [StandardHomeModelVariationOptionId] UNIQUEIDENTIFIER NOT NULL CONSTRAINT [PK_RSS_StandardHomeModelVariationOption]                       PRIMARY KEY,
    [StandardHomeModelId]                UNIQUEIDENTIFIER NOT NULL CONSTRAINT [FK_RSS_StandardHomeModelVariationOption_RSS_StandardHomeModel] FOREIGN KEY REFERENCES [dbo].[RSS_StandardHomeModel] ([StandardHomeModelId]),
    [VariationCategoryCode]              VARCHAR(100)     NOT NULL,
    [OptionName]                         VARCHAR(200)     NOT NULL,
    [SortOrder]                          SMALLINT         NOT NULL,
    [Deleted]                            BIT              NOT NULL CONSTRAINT [DF_RSS_StandardHomeModelVariationOption_Deleted] DEFAULT 0
)

-- Ok so... best way to do indexing on this?
-- Trial and error baby.
CREATE INDEX IX_RSS_StandardHomeModelOption_SearchIndexA
    ON dbo.RSS_StandardHomeModelOption(ClimateZone, NorthOffset, RoofConstruction)

CREATE INDEX IX_RSS_StandardHomeModelOption_ByRow
    ON dbo.RSS_StandardHomeModelOption([Row])

ALTER TABLE dbo.RSS_Client
    ADD EnergyLabsSettingsJson nvarchar(MAX)



--changeset LJT:190
-- Now require 1+ images per standard model (to support multiple storeys).
ALTER TABLE dbo.RSS_StandardHomeModel
    DROP FK_RSS_StandardHomeModel_RSS_FIle_FileId

ALTER TABLE dbo.RSS_StandardHomeModel
    DROP COLUMN HomePlanImageId

-- Link between standard models and files. The file could be an image plan, the excel file itself (not tracked yet) or
-- something else entirely. Refer to the general purpose 'category' column on the RSS_File for info. File name and so on
-- will be tracked on the RSS_File row itself.
CREATE TABLE dbo.RSS_StandardHomeModelFile
(
    StandardHomeModelFileId uniqueidentifier PRIMARY KEY,

    StandardHomeModelId uniqueidentifier                               NOT NULL
        CONSTRAINT FK_RSS_StandardHomeModelFile_StandardHomeModel_StandardHomeModelId
            REFERENCES dbo.RSS_StandardHomeModel (StandardHomeModelId),

    FileId              uniqueidentifier                               NULL
        CONSTRAINT FK_RSS_StandardHomeModelFile_File_FileId
            REFERENCES dbo.RSS_File (FileId),

    MetadataJson varchar(MAX) NULL,

    Deleted             bit                                            NOT NULL DEFAULT (0),
    CreatedOn           datetime                                       NOT NULL DEFAULT (CURRENT_TIMESTAMP),
    CreatedByName       varchar(50)                                    NOT NULL DEFAULT ('System'),
    ModifiedOn          datetime                                       NULL,
    ModifiedByName      varchar(50)                                    NULL,
)

CREATE INDEX IX_RSS_StandardHomeModelFile_StandardModelId
    ON dbo.RSS_StandardHomeModelFile(StandardHomeModelId)

-- Additional data captured from parsing process
ALTER TABLE dbo.RSS_StandardHomeModelOption
    ADD [Description] nvarchar(200) NULL,
        [Comments] nvarchar(500) NULL,
        [Active] bit NOT NULL
            CONSTRAINT DF_RSS_StandardHomeModelOption_Active
                DEFAULT (1)

-- Add 'features' to design
ALTER TABLE dbo.RSS_StandardHomeModel
    ADD FeaturesMasterSuiteAtFront bit NULL,
        FeaturesMasterSuiteAtRear bit NULL,
        FeaturesMasterSuiteGroundFloor bit NULL,
        FeaturesMasterSuiteFirstFloor bit NULL,
        FeaturesHomeTheatre bit NULL,
        FeaturesActivity bit NULL,
        FeaturesGames bit NULL,
        FeaturesStudyHomeOffice bit NULL,
        FeaturesScullery bit NULL,
        FeaturesAlfresco bit NULL,
        FeaturesRHCarAccess bit NULL,
        FeaturesLHCarAccess bit NULL,
        FeaturesRearCarAccess bit NULL

--changeset LJT:191
CREATE INDEX IX_RSS_StandardHomeModelOption_SearchIndexA_ByStandardModel
    ON dbo.RSS_StandardHomeModelOption(StandardHomeModelId, ClimateZone, NorthOffset)

--changeset LJT:192
-- Add ability to toggle on/off designs which appear in search
ALTER TABLE dbo.RSS_StandardHomeModel
    ADD IsActive bit  NOT NULL CONSTRAINT DF_RSS_StandardHomeModel_IsActive DEFAULT(1)

ALTER TABLE dbo.RSS_StandardHomeModel
    ADD NccBuildingClassification varchar(100) NULL

--changeset LJT:193
-- Permissions for Energy Labs. Used in combination with the 'clientportal' role to determine whether or not external
-- users have access to client portal + energy lab tool, or ONLY the 'energy lab' tool (which should look distinct). 
INSERT INTO dbo.aspnet_Roles (ApplicationId, RoleId, RoleName, LoweredRoleName, Description) VALUES
    ('D8C8B73E-45BB-4C48-90BA-DD37DD5C3A4B', '45C2C933-5176-4025-BEBC-053A30DAF4B2', 'Tools__EnergyLab', 'tools__energylab', 'Access Energy Lab')

--changeset LJT:194
ALTER TABLE dbo.RSS_StandardHomeModel
    ALTER COLUMN Width decimal (12, 2)

--changeset LJT:195
ALTER TABLE dbo.RSS_StandardHomeModelFile
    ADD SortOrder smallint NULL

--changeset LJT:196
ALTER TABLE dbo.RSS_StandardHomeModel
    ADD LowestLivingAreaFloorType varchar(100)

ALTER TABLE dbo.RSS_StandardHomeModel
    ADD HabitableRoomFloorAreaM2 decimal(12, 2)

--changeset LJT:197
ALTER TABLE dbo.RSS_StandardHomeModel
    ADD CostEstimateEnabled bit NOT NULL CONSTRAINT DF_RSS_StandardHomeModel_CostEstimateEnabled DEFAULT(0),
        CostEstimateDataJson nvarchar(MAX) NULL

--changeset LJT:198
ALTER TABLE dbo.RSS_StandardHomeModelOption
    ADD AssessmentMethod varchar(100)

--changeset LJT:199
-- the suburb dataset features duplicate rows which have no lat/lng data, making them useless. This simply purges them.
DELETE FROM dbo.RSS_Suburb WHERE StateCode = 'NSW' AND Latitude IS NULL

--changeset LJT:200
-- Lucky 200 - purge all suburbs with no lat/lng data.
DELETE FROM dbo.RSS_Suburb WHERE Latitude IS NULL

--changeset LJT:201
ALTER TABLE dbo.RSS_StandardHomeModel
    ALTER COLUMN FloorArea decimal(12, 2)
        
--changeset LJT:202
ALTER TABLE dbo.RSS_StandardHomeModel ADD VariableMetadataJson nvarchar(MAX) NULL

--changeset LJT:203
-- You can have half a bathroom.
ALTER TABLE dbo.RSS_StandardHomeModel
ALTER COLUMN NumberOfBathrooms decimal(12, 2)
    
--changeset LJT:204
-- More data capture.
ALTER TABLE dbo.RSS_StandardHomeModelOption
ADD SiteExposure varchar(100) NOT NULL CONSTRAINT DF_StandardHomeModelOption_SiteExposure DEFAULT('Suburban'),
    FloorHeight decimal(12, 2) NOT NULL CONSTRAINT DF_StandardHomeModelOption_FloorHeight DEFAULT(0.15),
    InteriorWallSolarAbsorptance decimal(12, 2) NOT NULL CONSTRAINT DF_StandardHomeModelOption_Iwsa DEFAULT(0.5),
    FloorSolarAbsorptance decimal(12, 2) NOT NULL CONSTRAINT DF_StandardHomeModelOption_Fsa DEFAULT(0.5)

-- changeset LJT:205
-- More data capture...
ALTER TABLE dbo.RSS_AssessmentComplianceBuilding
    ADD RoofsJson varchar(MAX)

-- changeset LJT:206
-- New role for General tab.
INSERT INTO dbo.aspnet_Roles (ApplicationId, RoleId, RoleName, LoweredRoleName, [Description]) VALUES
('D8C8B73E-45BB-4C48-90BA-DD37DD5C3A4B', '0E9BC59A-E654-4F33-A489-A4CABE37D8BB', 'Assessment__Tabs_Edit_General', 'assessment__tabs_edit_general', 'Edit General'),
('D8C8B73E-45BB-4C48-90BA-DD37DD5C3A4B', 'B59EA979-F318-4F04-B32F-98ACCBAA5997', 'Assessment__Tabs_View_General', 'assessment__tabs_view_general', 'View General')

-- changeset LJT:207
-- Add new "Living Areas" design field. No idea if this should be a decimal or int. 
-- Going with decimal as you can have half a toilet, remember.
ALTER TABLE dbo.RSS_StandardHomeModel
    ADD LivingAreas decimal(12, 2)

ALTER TABLE dbo.RSS_StandardHomeModelOption
    ALTER COLUMN FloorHeight decimal(12, 6)


-- = = = = = = = = = = =
-- Create AspNet tables
-- = = = = = = = = = = =

-- Insert into AspNetUsers
INSERT INTO AspNetUsers
(      [Id],                    [UserName],                [PasswordHash],                                                                                                                            [SecurityStamp], [EmailConfirmed], [PhoneNumber], [PhoneNumberConfirmed], [TwoFactorEnabled], [LockoutEndDateUtc],                   [LockoutEnabled], [AccessFailedCount], [ApplicationId],                [LoweredUserName],                [MobileAlias],                [IsAnonymous],                    [LastActivityDate],                [LegacyPasswordHash],           [MobilePIN],                     [Email],                     [LoweredEmail],                     [PasswordQuestion],                     [PasswordAnswer],                     [IsApproved],                     [IsLockedOut],                     [CreateDate],                     [LastLoginDate],                     [LastPasswordChangedDate],                     [LastLockoutDate],                     [FailedPasswordAttemptCount],                     [FailedPasswordAnswerAttemptWindowStart],                     [FailedPasswordAnswerAttemptCount],                     [FailedPasswordAttemptWindowStart],                     [Comment])
SELECT [aspnet_Users].[UserId], [aspnet_Users].[UserName], ([aspnet_Membership].[Password] + '|' + CAST([aspnet_Membership].[PasswordFormat] AS VARCHAR) + '|' + [aspnet_Membership].[PasswordSalt]), NewID(),         'true',           NULL,          'false',                'false',            [aspnet_Membership].[LastLockoutDate], 'true',           '0',                 [aspnet_Users].[ApplicationId], [aspnet_Users].[LoweredUserName], [aspnet_Users].[MobileAlias], [aspnet_Users].[IsAnonymous],     [aspnet_Users].[LastActivityDate], [aspnet_Membership].[Password], [aspnet_Membership].[MobilePIN], [aspnet_Membership].[Email], [aspnet_Membership].[LoweredEmail], [aspnet_Membership].[PasswordQuestion], [aspnet_Membership].[PasswordAnswer], [aspnet_Membership].[IsApproved], [aspnet_Membership].[IsLockedOut], [aspnet_Membership].[CreateDate], [aspnet_Membership].[LastLoginDate], [aspnet_Membership].[LastPasswordChangedDate], [aspnet_Membership].[LastLockoutDate], [aspnet_Membership].[FailedPasswordAttemptCount], [aspnet_Membership].[FailedPasswordAnswerAttemptWindowStart], [aspnet_Membership].[FailedPasswordAnswerAttemptCount], [aspnet_Membership].[FailedPasswordAttemptWindowStart], [aspnet_Membership].[Comment]
FROM [aspnet_Users]
LEFT OUTER JOIN [aspnet_Membership] ON [aspnet_Membership].[ApplicationId] = [aspnet_Users].[ApplicationId]
            AND [aspnet_Users].[UserId] = [aspnet_Membership].[UserId]

-- Insert into AspNetRoles
INSERT INTO [dbo].[AspNetRoles]
(      [Id],     [Name],     [Description])
SELECT [RoleId], [RoleName], [Description]
FROM [dbo].[aspnet_Roles]

-- Insert into AspNetUserRoles
INSERT INTO [dbo].[AspNetUserRoles]
(      [UserId], [RoleId])
SELECT [UserId], [RoleId]
FROM [dbo].[aspnet_UsersInRoles]

-- Change RSS_UsersClient's UserId foreign key from aspnet_Users to AspNetUsers
-- Change RSS_UsersClient's UserId type from UNIQUEIDENTIFIER to NVARCHAR
IF OBJECT_ID('FK_RSS_UsersClient_aspnet_Users') IS NOT NULL BEGIN
    ALTER TABLE [dbo].[RSS_UsersClient] DROP CONSTRAINT [FK_RSS_UsersClient_aspnet_Users];
END
ALTER TABLE [dbo].[RSS_UsersClient] ALTER COLUMN [UserId] NVARCHAR(128) NOT NULL;
ALTER TABLE [dbo].[RSS_UsersClient] ADD CONSTRAINT [FK_RSS_UsersClient_AspNetUsers] FOREIGN KEY ([UserId]) REFERENCES [dbo].[AspNetUsers]([Id])

-- Change RSS_User's AspNetUserId foreign key from aspnet_Users to AspNetUsers
-- Change RSS_User's AspNetUserId type from UNIQUEIDENTIFIER to NVARCHAR
IF OBJECT_ID('FK_aspnet_User_RSS_User_Id') IS NOT NULL BEGIN
    ALTER TABLE [dbo].[RSS_User] DROP CONSTRAINT [FK_aspnet_User_RSS_User_Id];
END
ALTER TABLE [dbo].[RSS_User] ALTER COLUMN [AspNetUserId] NVARCHAR(128) NULL;
ALTER TABLE [dbo].[RSS_User] ADD CONSTRAINT [FK_RSS_User_AspNetUsers] FOREIGN KEY ([AspNetUserId]) REFERENCES [dbo].[AspNetUsers]([Id])

-- Change RSS_User's AspNetUserId foreign key from aspnet_Users to AspNetUsers
-- Change RSS_User's AspNetUserId type from UNIQUEIDENTIFIER to NVARCHAR
IF OBJECT_ID('FK_RSS_MapToUserRoles_aspnet_Roles') IS NOT NULL BEGIN
    ALTER TABLE [dbo].[RSS_RolesBusinessRole] DROP CONSTRAINT [FK_RSS_MapToUserRoles_aspnet_Roles];
END
ALTER TABLE [dbo].[RSS_RolesBusinessRole] ALTER COLUMN [aspnet_RoleId] NVARCHAR(128) NULL;
ALTER TABLE [dbo].[RSS_RolesBusinessRole] ADD CONSTRAINT [FK_RSS_RolesBusinessRole_AspNetRoles] FOREIGN KEY ([aspnet_RoleId]) REFERENCES [dbo].[AspNetRoles]([Id])

-- Drop Views
IF OBJECT_ID('vw_aspnet_MembershipUsers') IS NOT NULL BEGIN
    DROP VIEW [dbo].[vw_aspnet_MembershipUsers];
END
IF OBJECT_ID('vw_aspnet_Roles') IS NOT NULL BEGIN
    DROP VIEW [dbo].[vw_aspnet_Roles];
END
IF OBJECT_ID('vw_aspnet_Users') IS NOT NULL BEGIN
    DROP VIEW [dbo].[vw_aspnet_Users];
END
IF OBJECT_ID('vw_aspnet_UsersInRoles') IS NOT NULL BEGIN
    DROP VIEW [dbo].[vw_aspnet_UsersInRoles];
END

-- = = = = = = = = = = =

--changeset JCC:045
INSERT INTO [dbo].[RSS_BusinessRole]
([RoleCode], [Description])
VALUES
('User',     'Normal user that can view data.')

INSERT INTO [dbo].[RSS_RolesBusinessRole]
([RoleCode], [aspnet_RoleId])
VALUES
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'ViewComplianceCost')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Admin__BusinessUnit_View')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Admin__Client_View')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Admin__Template_View')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Admin__User_View')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Assigned_View_Other')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Assigned_View_Self')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_AInProgress')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_ACancelled')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_ARecertification')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_ASuperseded')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_AIssued')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_AComplete')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_ACompliance')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_AOptionSelected')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_ADraft')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Status_APreliminaryReview')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Address')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Climate')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_General')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Openings')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Simulation')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Site')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Reports')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Invoicing')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Construction')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_AerialImage')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Assessment')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Drawings')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Map')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Services')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Results')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Lot')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Design')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Assessment__Tabs_View_Analytics')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Tools__EnergyLab')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Tools__GlazingCalculator')),
('User',     (SELECT TOP(1) [Id] FROM [dbo].[AspNetRoles] WHERE [Name] = 'Tools__WholeOfHomeCalculator'))


--changeset JCC:004
EXEC sp_rename 'RSS_StandardHomeModelOption.ClimateZone', 'NatHERSClimateZone'

--changeset JCC:005
ALTER TABLE dbo.RSS_StandardHomeModelOption
ADD [CeilingFans] VARCHAR(100) NOT NULL DEFAULT 'Nil',
    [RecessedLightFittings] VARCHAR(100) NOT NULL DEFAULT 'Nil'

--changeset JCC:006
ALTER TABLE dbo.RSS_StandardHomeModel
ADD [Depth] decimal(12, 2) NULL,
    [HouseArea] decimal(12, 2) NULL,
    [CarParkingArea] decimal(12, 2) NULL,
    [OutdoorAlfrescoArea] decimal(12, 2) NULL

--changeset JCC:007
ALTER TABLE [dbo].[RSS_StandardHomeModel]
ADD [FeaturesKitchenLivingDiningGroundFloor] [bit] NULL,
    [FeaturesKitchenLivingDiningUpperFloor] [bit] NULL,
    [FeaturesKitchenLivingDiningRear] [bit] NULL,
    [FeaturesKitchenLivingDiningFront] [bit] NULL,
    [FeaturesWalkInPantry] [bit] NULL,
    [FeaturesSecondLivingLounge] [bit] NULL,
    [FeaturesRetreat] [bit] NULL,
    [FeaturesCarport] [bit] NULL,
    [FeaturesGarage] [bit] NULL,
    [FeaturesRearAccessRearLoaded] [bit] NULL

--changeset JCC:008
      -- [RSS_StandardHomeModel] no longer attached to [RSS_Client], now attached to [RSS_Project] which attaches to [RSS_Client]
ALTER TABLE dbo.RSS_StandardHomeModel
ADD [ProjectId] UNIQUEIDENTIFIER NULL CONSTRAINT [FK_RSS_StandardHomeModel_RSS_Project] REFERENCES [dbo].[RSS_Project] ([ProjectId])
      -- [EnergyLabSettingsJson] moved to [RSS_Project]
ALTER TABLE dbo.RSS_Client
DROP COLUMN [EnergyLabsSettingsJson]

--changeset JCC:009
      -- [THR-381] Change standard model fields to have more than 2 decimal places
ALTER TABLE [RSS_StandardHomeModel] ALTER COLUMN [Width]                    DECIMAL(16, 6)
ALTER TABLE [RSS_StandardHomeModel] ALTER COLUMN [Depth]                    DECIMAL(16, 6)
ALTER TABLE [RSS_StandardHomeModel] ALTER COLUMN [HouseArea]                DECIMAL(16, 6)
ALTER TABLE [RSS_StandardHomeModel] ALTER COLUMN [CarParkingArea]           DECIMAL(16, 6)
ALTER TABLE [RSS_StandardHomeModel] ALTER COLUMN [OutdoorAlfrescoArea]      DECIMAL(16, 6)
ALTER TABLE [RSS_StandardHomeModel] ALTER COLUMN [FloorArea]                DECIMAL(16, 6)
ALTER TABLE [RSS_StandardHomeModel] ALTER COLUMN [HabitableRoomFloorAreaM2] DECIMAL(16, 6)

      -- [THR-388] Add new fields
ALTER TABLE [RSS_StandardHomeModel] ADD [HouseWidth]                            DECIMAL(16, 6) NULL,
                                        [HouseDepth]                            DECIMAL(16, 6) NULL,
                                        [GlassToHouseAreaRatio]                 DECIMAL(16, 6) NULL,
                                        [GlassToHousePerimiterRatio]            DECIMAL(16, 6) NULL,
                                        [ConditionedFloorArea]                  DECIMAL(16, 6) NULL,
                                        [ConditionedNonHabitableFloorAreaRatio] DECIMAL(16, 6) NULL,
                                        [GlassToInternalFloorAreaRatio]         DECIMAL(16, 6) NULL,
                                        [GlassToConditionedFloorAreaRatio]      DECIMAL(16, 6) NULL,
                                        [GlassToExteriorWallRatio]              DECIMAL(16, 6) NULL,
                                        [FrontElevationWallRatio]               DECIMAL(16, 6) NULL,
                                        [RearElevationWallRatio]                DECIMAL(16, 6) NULL,
                                        [LeftElevationWallRatio]                DECIMAL(16, 6) NULL,
                                        [RightElevationWallRatio]               DECIMAL(16, 6) NULL,
                                        [FeaturesAlfrescoCentre]                BIT NULL,
                                        [FeaturesLHOutdoorLiving]               BIT NULL,
                                        [FeaturesRHOutdoorLiving]               BIT NULL,
                                        [FeaturesOutdoorCourtyard]              BIT NULL
                                        
ALTER TABLE [RSS_Project] ALTER COLUMN [LotArea] DECIMAL(16, 6)

      -- [THR-415] Add 'Category' fields
ALTER TABLE [RSS_StandardHomeModel] ADD [CategoryDisplayHome]  BIT NULL,
                                        [CategoryFarmhouse]    BIT NULL,
                                        [CategoryNarrowLot]    BIT NULL,
                                        [CategoryNewDesign]    BIT NULL,
                                        [CategorySingleStorey] BIT NULL,
                                        [CategoryTwoStorey]    BIT NULL,
                                        [CategoryThreeStorey]  BIT NULL

      -- [THR-421] Add label to model drawings
ALTER TABLE [RSS_StandardHomeModelFile] ADD [Label] VARCHAR(100) NULL

      -- [THR-423] New "Site Cover" field
ALTER TABLE dbo.RSS_StandardHomeModel
ADD [SiteCover] DECIMAL(16, 6) NULL

      -- [THR-396] New design variables
ALTER TABLE dbo.RSS_StandardHomeModelOption
ADD [ExteriorDoorSolarAbsorptance] DECIMAL(12,2) NOT NULL DEFAULT 0.5,
    [GarageDoorSolarAbsorptance] DECIMAL(12,2) NOT NULL DEFAULT 0.5

--changeset JCC:010
    -- Allow 8 decimals for LotWidth and LotLength
ALTER TABLE dbo.RSS_AssessmentProjectDetail
ALTER COLUMN [LotWidth] DECIMAL(15, 8)
ALTER TABLE dbo.RSS_AssessmentProjectDetail
ALTER COLUMN [LotLength] DECIMAL(15, 8)



--changeset JCC:012
    -- Set all notification rules to disabled before pushing live
UPDATE [dbo].[RSS_NotificationRule]
SET [Enabled] = 0

--changeset JCC:013
      -- [THR-471] New design variable
ALTER TABLE dbo.RSS_StandardHomeModelOption
ADD [CeilingConstruction] VARCHAR(100) NOT NULL DEFAULT 'Nil'

--changeset JCC:014
ALTER TABLE dbo.RSS_StandardHomeModel
ADD [WohFloorArea] decimal(12, 2) NULL


--changeset JCC:016
ALTER TABLE [dbo].[RSS_ClientDefault]
ADD [SpaceHeatingServiceTypeCode] VARCHAR(100) NULL,
    [SpaceHeatingGems2019Rating] DECIMAL(4, 1) NULL,
    [SpaceCoolingServiceTypeCode] VARCHAR(100) NULL,
    [SpaceCoolingGems2019Rating] DECIMAL(4, 1) NULL,
    [WaterHeatingServiceTypeCode] VARCHAR(100) NULL,
    [WaterHeatingGems2019Rating] DECIMAL(4, 1) NULL,
    [SwimmingPoolExists] BIT NULL,
    [SwimmingPoolVolume] DECIMAL(4, 1) NULL,
    [SwimmingPoolGems2019Rating] DECIMAL(4, 1) NULL,
    [SpaExists] BIT NULL,
    [SpaVolume] DECIMAL(4, 1) NULL,
    [SpaGems2019Rating] DECIMAL(4, 1) NULL,
    [PhotovoltaicExists] BIT NULL,
    [PhotovoltaicCapacity] DECIMAL(4, 1) NULL

--changeset JCC:017
ALTER TABLE [dbo].[RSS_AssessmentComplianceOption]
ADD [NCC2022Json] VARCHAR(MAX) NULL


--changeset JCC:021
ALTER TABLE [dbo].[RSS_AssessmentComplianceBuilding]
ADD [EnvelopeSummaryJson] VARCHAR(MAX) NULL

--changeset JCC:022
ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] ADD [UseCustomClientName] BIT NULL,
                                                    [CustomClientName] VARCHAR(500) NULL

--changeset JCC:023
ALTER TABLE [dbo].[RSS_StandardHomeModel] ADD [View3dFloorPlans] BIT NULL,
                                              [FloorplannerLink] VARCHAR(200) NULL

--changeset JCC:026
ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] ADD [ComplianceCost] DECIMAL NULL

--changeset JCC:027
ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] ALTER COLUMN [ComplianceCost] DECIMAL(15,2) NULL

--changeset JCC:028
ALTER TABLE [dbo].[RSS_Assessment] ADD [CertificateDateOverride] DATETIME NULL

--changeset JCC:029
      -- [THR-495] Add 'Category' and 'Features' fields
ALTER TABLE [RSS_StandardHomeModel] ADD [CategoryGrannyFlat] BIT NULL,
                                        [CategoryRearLoaded] BIT NULL,
                                        [FeaturesDressingRoom] BIT NULL,
                                        [FeaturesComputerNook] BIT NULL,
                                        [FeaturesBalcony] BIT NULL,
                                        [FeaturesVerandah] BIT NULL,
                                        [FeaturesWorkshop] BIT NULL

--changeset JCC:032
ALTER TABLE dbo.RSS_StandardHomeModel ADD FeaturesMultipurposeRoom BIT NULL

--changeset JCC:033 [Adjusted for this ScriptsForLive.sql]
ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] DROP COLUMN [CornerBlock]
ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] ADD [CornerBlock] VARCHAR(100) NULL


--changeset JCC:034
    -- [THR-523]

    -- Add "FuelType" options
INSERT INTO [dbo].[RSS_ServiceFuelType]
([ServiceFuelTypeCode], [Title], [SortOrder])
VALUES
('Solid',               'Solid', 90),
('Wood',                'Wood',  100)

    -- New "PumpType" field
CREATE TABLE [dbo].[RSS_ServicePumpType]
(
    ServicePumpTypeCode VARCHAR(50)  NOT NULL PRIMARY KEY,
    Title               NVARCHAR(50) NOT NULL,
    SortOrder           SMALLINT     NOT NULL,
    Deleted             BIT          NOT NULL CONSTRAINT DF_RSS_ServicePumpType_Deleted DEFAULT 0
)
ALTER TABLE [dbo].[RSS_ServiceTemplate] ADD [ServicePumpTypeCode] VARCHAR(50) NULL CONSTRAINT [FK_RSS_ServiceTemplate_RSS_ServicePumpType_ServicePumpTypeCode] REFERENCES [dbo].[RSS_ServicePumpType](ServicePumpTypeCode)
INSERT INTO [dbo].[RSS_ServicePumpType]
([ServicePumpTypeCode], [Title],          [SortOrder]) 
VALUES
('SingleSpeed',         'Single Speed',   10),
('DualSpeed',           'Dual Speed',     20),
('VariableSpeed',       'Variable Speed', 30)

    -- Create [RSS_ServiceBatteryType]
CREATE TABLE [dbo].[RSS_ServiceBatteryType]
(
    [ServiceBatteryTypeCode] VARCHAR(50)  NOT NULL PRIMARY KEY,
    [Title]                  NVARCHAR(50) NOT NULL,
    [SortOrder]              SMALLINT     NOT NULL,
    [Deleted]                BIT          NOT NULL CONSTRAINT DF_RSS_ServiceBatteryType_Deleted DEFAULT 0
)
INSERT INTO [dbo].[RSS_ServiceBatteryType]
([ServiceBatteryTypeCode], [Title],        [SortOrder])
VALUES
('None',                   'None',         10),
('LithiumIon',             'Lithium Ion',  20),
('LeadAcid',               'Lead Acid',    30),
('Zinc Bromide',           'Zinc Bromide', 40)

    -- New [RSS_ServiceTemplate] fields
ALTER TABLE [dbo].[RSS_ServiceTemplate] ADD [Area]                    DECIMAL(9, 2) NULL,
                                            [Azimuth]                 DECIMAL(9, 2) NULL,
                                            [Pitch]                   DECIMAL(9, 2) NULL,
                                            [ShadeFactor]             DECIMAL(9, 2) NULL,
                                            [InverterCapacity]        DECIMAL(9, 2) NULL,
                                            [ServiceBatteryTypeCode]  VARCHAR(50)   NULL CONSTRAINT [FK_RSS_ServiceTemplate_RSS_ServiceBatteryType] REFERENCES [dbo].[RSS_ServiceBatteryType]([ServiceBatteryTypeCode]),
                                            [BatteryCapacity]         DECIMAL(9, 2) NULL

    -- Create "Cooktop" category
INSERT INTO [dbo].[RSS_ServiceCategory]
([ServiceCategoryCode], [Title],   [Description], [SortOrder], [HasChildRows], [ElementCode], [ServiceTypeUIDisplay], [HeatingSystemTypeUIDisplay])
VALUES
('Cooktop',             'Cooktop', '',            33,          0,              NULL,          'Cooktop Type',         NULL)
INSERT INTO [dbo].[RSS_ServiceType]
([ServiceTypeCode],  [Title],     [SortOrder], [ForServiceCategory])
VALUES
('CooktopElectric',  'Electric',  250,         'Cooktop'),
('CooktopInduction', 'Induction', 260,         'Cooktop'),
('CooktopGas',       'Gas',       270,         'Cooktop'),
('CooktopLPG',       'LPG',       280,         'Cooktop')

    -- Create "Oven" category
INSERT INTO [dbo].[RSS_ServiceCategory]
([ServiceCategoryCode], [Title],   [Description], [SortOrder], [HasChildRows], [ElementCode], [ServiceTypeUIDisplay], [HeatingSystemTypeUIDisplay])
VALUES
('Oven',                'Oven',    '',            37,          0,              NULL,          'Oven Type',         NULL)
INSERT INTO [dbo].[RSS_ServiceType]
([ServiceTypeCode], [Title],     [SortOrder], [ForServiceCategory])
VALUES
('OvenElectric',    'Electric',  290,         'Oven'),
('OvenGas',         'Gas',       300,         'Oven'),
('OvenLPG',         'LPG',       310,         'Oven')



--changeset JCC:035
    -- [THR-535]
    -- Now keeping short version of LGA's
ALTER TABLE [dbo].[RSS_LocalGovernmentArea] ADD [NameShort] [varchar](100) NULL
ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] ADD [LocalGovernmentAuthorityShort] [varchar](100) NULL
ALTER TABLE [dbo].[RSS_Project] ADD [LGAShort] [varchar](100) NULL


--changeset JCC:036
ALTER TABLE [dbo].[RSS_Assessment] ADD [BushFireProneUnknown] BIT NULL

--changeset JCC:037
    -- [THR-536]
ALTER TABLE [dbo].[RSS_StandardHomeModel] ADD [DisplayFloorArea] DECIMAL(16, 6) NULL,
                                              [FloorAreaHabitableRooms] DECIMAL(16, 6) NULL,
                                              [HabitableRooms] SMALLINT NULL

--changeset JCC:038
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesSecondLivingLounge',   'FeaturesSecondLiving',    'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesStudyHomeOffice',      'FeaturesHomeOffice',      'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesLHCarAccess',          'FeaturesLHGarage',        'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesRHCarAccess',          'FeaturesRHGarage',        'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesRearAccessRearLoaded', 'FeaturesRearAccess',      'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesOutdoorCourtyard',     'FeaturesCourtyard',       'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesAlfresco',             'FeaturesOutdoorLiving',   'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesAlfrescoCentre',       'FeaturesRCOutdoorLiving', 'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesLHOutdoorLiving',      'FeaturesRLOutdoorLiving', 'COLUMN'
EXEC sp_rename 'dbo.RSS_StandardHomeModel.FeaturesRHOutdoorLiving',      'FeaturesRROutdoorLiving', 'COLUMN'

ALTER TABLE [RSS_StandardHomeModel] ADD [CategoryAcreage] BIT NULL,
                                        [CategoryDualOccupancy] BIT NULL,
                                        [CategoryDuplex] BIT NULL,
                                        [CategorySplitLevel] BIT NULL,
                                        [FeaturesKitchenLivingDiningMiddle] BIT NULL,
                                        [FeaturesButlersPantry] BIT NULL,
                                        [FeaturesMasterSuiteAtMiddle] BIT NULL,
                                        [FeaturesHisHerWir] BIT NULL,
                                        [FeaturesWalkInRobe] BIT NULL,
                                        [FeaturesHomeCinema] BIT NULL,
                                        [FeaturesMedia] BIT NULL,
                                        [FeaturesEntertainment] BIT NULL,
                                        [FeaturesFamily] BIT NULL,
                                        [FeaturesFormalLounge] BIT NULL,
                                        [FeaturesLeisure] BIT NULL,
                                        [FeaturesLounge] BIT NULL,
                                        [FeaturesRumpus] BIT NULL,
                                        [FeaturesENook] BIT NULL,
                                        [FeaturesStudy] BIT NULL,
                                        [FeaturesStudyNook] BIT NULL,
                                        [FeaturesCellar] BIT NULL,
                                        [FeaturesCloakRoom] BIT NULL,
                                        [FeaturesGuestBedroom] BIT NULL,
                                        [FeaturesGym] BIT NULL,
                                        [FeaturesMudRoom] BIT NULL,
                                        [FeaturesNannysQuarters] BIT NULL,
                                        [FeaturesPowderRoom] BIT NULL,
                                        [FeaturesStoreRoom] BIT NULL,
                                        [FeaturesWalkInLinen] BIT NULL,
                                        [FeaturesLHCarport] BIT NULL,
                                        [FeaturesRHCarport] BIT NULL,
                                        [FeaturesRearLoaded] BIT NULL,
                                        [FeaturesAlfresco] BIT NULL,
                                        [FeaturesMLAlfresco] BIT NULL,
                                        [FeaturesMRAlfresco] BIT NULL,
                                        [FeaturesRCAlfresco] BIT NULL,
                                        [FeaturesRLAlfresco] BIT NULL,
                                        [FeaturesRRAlfresco] BIT NULL,
                                        [FeaturesFrontBalcony] BIT NULL,
                                        [FeaturesRearBalcony] BIT NULL,
                                        [FeaturesLHCourtyard] BIT NULL,
                                        [FeaturesRHCourtyard] BIT NULL,
                                        [FeaturesMLOutdoorLiving] BIT NULL,
                                        [FeaturesMROutdoorLiving] BIT NULL

--changeset JCC:039
    -- [THR-543] New "ServiceType" values
INSERT INTO [dbo].[RSS_ServiceType]
([ServiceTypeCode],          [Title],                                [SortOrder], [ForServiceCategory])
VALUES
('ElectricResistanceDucted', 'Electric Resistance (Ducted)',         320,         'SpaceHeatingSystem'),
('HydronicUnderfloor',       'Hydronic Underfloor',                  330,         'SpaceHeatingSystem'),
('EvaporativeRoom',          'Evaporative (Room)',                   340,         'SpaceCoolingSystem'),
('ElectricInstantaneous',    'Electric Instantaneous',               350,         'HotWaterSystem'),
('SolarElectricBoost',       'Solar with Electric Boost (Off-Peak)', 360,         'HotWaterSystem'),
('Wood',                     'Wood',                                 370,         'HotWaterSystem')


--changeset JCC:040

    -- Update [RSS_OpeningStyle] SortOrders so in future can add new SortOrder in between
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 10  WHERE [OpeningStyleCode] = 'AwningWindow'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 20  WHERE [OpeningStyleCode] = 'BiFoldDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 30  WHERE [OpeningStyleCode] = 'BiFoldWindow'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 40  WHERE [OpeningStyleCode] = 'CasementWindow'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 50  WHERE [OpeningStyleCode] = 'CasementDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 60  WHERE [OpeningStyleCode] = 'DoubleHungWindow'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 70  WHERE [OpeningStyleCode] = 'FixedWindow'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 80  WHERE [OpeningStyleCode] = 'FrenchDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 90  WHERE [OpeningStyleCode] = 'GarageDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 100 WHERE [OpeningStyleCode] = 'HingedDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 110 WHERE [OpeningStyleCode] = 'Louvre'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 120 WHERE [OpeningStyleCode] = 'PivotDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 130 WHERE [OpeningStyleCode] = 'PivotWindow'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 140 WHERE [OpeningStyleCode] = 'SlidingDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 150 WHERE [OpeningStyleCode] = 'SlidingWindow'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 160 WHERE [OpeningStyleCode] = 'StackingDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 170 WHERE [OpeningStyleCode] = 'TiltAndTurnDoor'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 180 WHERE [OpeningStyleCode] = 'TiltAndTurnWindow'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 190 WHERE [OpeningStyleCode] = 'CentrePivot'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 200 WHERE [OpeningStyleCode] = 'TopHung'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 210 WHERE [OpeningStyleCode] = 'Fixed'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 980 WHERE [OpeningStyleCode] = 'FloorOpening'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 990 WHERE [OpeningStyleCode] = 'Other'
UPDATE [dbo].[RSS_OpeningStyle] SET [SortOrder] = 990 WHERE [OpeningStyleCode] = 'PermanentOpening'

    -- [THR-549] Openings Mapping Changes

UPDATE [dbo].[RSS_OpeningStyle] SET [Title] = 'Louvre Window' WHERE [OpeningStyleCode] = 'Louvre'

INSERT INTO [dbo].[RSS_OpeningStyle]
([OpeningStyleCode], [Title],         [Type], [DefaultOpenability], [SortOrder], [NccOpeningStyleCode])
VALUES
('CurtainWall',      'Curtain Wall',  'wall', 90,                   124,         'Fixed'),
('WindowWall',       'Window Wall',   'wall', 90,                   126,         'Fixed')


--changeset JCC:041
    -- [THR-552] Change all "Supplier" to "Manufacturer"

exec sp_rename 'dbo.RSS_Supplier', 'RSS_Manufacturer'
exec sp_rename 'dbo.RSS_Manufacturer.SupplierId', 'ManufacturerId'
exec sp_rename 'dbo.RSS_ServiceTemplate.SupplierId', 'ManufacturerId'
exec sp_rename 'dbo.RSS_SurfaceTemplate.SupplierId', 'ManufacturerId'
exec sp_rename 'dbo.RSS_OpeningTemplate.SupplierId', 'ManufacturerId'
exec sp_rename 'dbo.RSS_Colour.SupplierId', 'ManufacturerId'

-- RSS_SurfaceTemplateOpeningTemplateView will be created at the end of the script

-- RSS_ServiceTemplateView will be created at the end of the script

--changeset JCC:042
ALTER TABLE [dbo].[RSS_ClientDefault]
ADD [ProjectDescription]        VARCHAR(100) NULL,
    [NccBuildingClassification] VARCHAR(100) NULL




--changeset JCC:044
ALTER TABLE [dbo].[RSS_User]
ADD [IsGoogleLinked] BIT NOT NULL DEFAULT 0,
    [IsMicrosoftLinked] BIT NOT NULL DEFAULT 0


--changeset JCC:045
    -- [THR-544] Home Design Variations
ALTER TABLE [dbo].[RSS_StandardHomeModel] ADD [VariationOptionsSettingsJson] VARCHAR(MAX)         NULL,
                                              [IsVariationOfHomeModelId]     UNIQUEIDENTIFIER     NULL CONSTRAINT FK_RSS_StandardHomeModel_RSS_StandardHomeModel FOREIGN KEY REFERENCES [dbo].[RSS_StandardHomeModel] ([StandardHomeModelId]),
                                              [VariationFloorplanId]         UNIQUEIDENTIFIER     NULL CONSTRAINT FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_Floorplan     FOREIGN KEY REFERENCES [dbo].[RSS_StandardHomeModelVariationOption] ([StandardHomeModelVariationOptionId]),
                                              [VariationDesignOptionId]      UNIQUEIDENTIFIER     NULL CONSTRAINT FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_DesignOption  FOREIGN KEY REFERENCES [dbo].[RSS_StandardHomeModelVariationOption] ([StandardHomeModelVariationOptionId]),
                                              [VariationFacadeId]            UNIQUEIDENTIFIER     NULL CONSTRAINT FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_Facade        FOREIGN KEY REFERENCES [dbo].[RSS_StandardHomeModelVariationOption] ([StandardHomeModelVariationOptionId]),
                                              [VariationSpecificationId]     UNIQUEIDENTIFIER     NULL CONSTRAINT FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_Specification FOREIGN KEY REFERENCES [dbo].[RSS_StandardHomeModelVariationOption] ([StandardHomeModelVariationOptionId]),
                                              [VariationConfigurationId]     UNIQUEIDENTIFIER     NULL CONSTRAINT FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_Configuration FOREIGN KEY REFERENCES [dbo].[RSS_StandardHomeModelVariationOption] ([StandardHomeModelVariationOptionId]),
                                              [IsDefaultVariation]           BIT              NOT NULL CONSTRAINT DF_RSS_StandardHomeModel_IsDefaultVariation DEFAULT (0)
--changeset JCC:170924
    -- [THR-572] Have manual sort for Home Design Variations
ALTER TABLE [dbo].[RSS_StandardHomeModel] ADD [SortOrder] INT NOT NULL CONSTRAINT [DF_RSS_StandardHomeModel_SortOrder] DEFAULT (999)


--changeset JCC:011024
    -- [THR-579] Can move Projects and Home Designs Up/Down
ALTER TABLE [dbo].[RSS_Project] ADD [SortOrder] INT NOT NULL CONSTRAINT [DF_RSS_Project_SortOrder] DEFAULT (999)


--changeset JCC:261124
ALTER TABLE [dbo].[RSS_StandardHomeModel] ADD [DrawingAreasJson] VARCHAR(MAX) NULL

--chnageset JCC:220125
    -- [THR-628]
ALTER TABLE [dbo].[RSS_StandardHomeModel] ADD [ZoneSummaryBuildingDataJson] VARCHAR(MAX) NULL

--changeset JCC:060225
    -- [THR-650]
ALTER TABLE [dbo].[RSS_Client] ADD [ClientCostItemsJson] VARCHAR(MAX) NULL

--changeset JCC:110225
    -- [THR-643]
ALTER TABLE [dbo].[RSS_StandardHomeModel] ADD [NumberOfBedroomsVarRefJson]        VARCHAR(MAX) NULL,
                                              [NumberOfBathroomsVarRefJson]       VARCHAR(MAX) NULL,
                                              [LivingAreasVarRefJson]             VARCHAR(MAX) NULL,
                                              [DisplayFloorAreaVarRefJson]        VARCHAR(MAX) NULL,
                                              [WohFloorAreaVarRefJson]            VARCHAR(MAX) NULL,
                                              [FloorAreaHabitableRoomsVarRefJson] VARCHAR(MAX) NULL,
                                              [SiteCoverVarRefJson]               VARCHAR(MAX) NULL

--changeset JCC:260225
    -- [THR-667]
ALTER TABLE [dbo].[RSS_AssessmentComplianceBuilding] ADD [ZoneSummaryJson]                VARCHAR(MAX) NULL
ALTER TABLE [dbo].[RSS_AssessmentComplianceBuilding] ADD [EnvelopeSummaryConditionedJson] VARCHAR(MAX) NULL
ALTER TABLE [dbo].[RSS_AssessmentComplianceBuilding] ADD [EnvelopeSummaryHabitableJson]   VARCHAR(MAX) NULL

--changeset JCC:260225.2
    -- [THR-669]
ALTER TABLE [dbo].[RSS_AssessmentDrawing] ADD [ToStamp] BIT NOT NULL CONSTRAINT [DF_RSS_AssessmentDrawing_ToStamp] DEFAULT(1)


--changeset JCC:040325_02
ALTER TABLE [dbo].[RSS_ClientDefault] ADD [IncludeDrawingsInReport] BIT NOT NULL CONSTRAINT [DF_RSS_ClientDefault_IncludeDrawingsInReport] DEFAULT(1),
                                          [StampDrawings] BIT NOT NULL CONSTRAINT [DF_RSS_ClientDefault_StampDrawings] DEFAULT(1)


--changeset JCC:050325
    -- [THR-672]
    -- User Permissions

-- AspNetRoles
ALTER TABLE [dbo].[AspNetRoles] ADD [SortOrder]    INT              NOT NULL CONSTRAINT [DF_RSS_AspNetRoles_SortOrder] DEFAULT(999),
                                    [ParentRoleId] UNIQUEIDENTIFIER NULL,
                                    [Type]         VARCHAR(50)      NOT NULL CONSTRAINT [DF_RSS_AspNetRoles_Type]      DEFAULT('Checkbox')
                

-- RSS_JobAssessmentProjectDetail_View will be created at the end of the script


-- RSS_JobAnalyticsDetail_View will be created at the end of the script









--changeset JCC:130325
ALTER TABLE [dbo].[RSS_NotificationRule] ADD [ClientSelectionsListJson] VARCHAR(MAX)

--changeset JCC:170325
ALTER TABLE [dbo].[ELMAH_Error] ALTER COLUMN [Message] VARCHAR(MAX)



--changeset JCC:050325
    -- [THR-672]
    -- User Permissions

-- --------------- --
-- Navigation Menu --
-- --------------- --
--                                      [Id]                                    [Name]                                       [Discriminator], [Description]              [SortOrder] [ParentRoleId]                            [Type]
INSERT INTO [dbo].[AspNetRoles] VALUES ('6B3A9031-4E8E-42AB-AF32-65172F17959B', 'naviation_menu__home',                      NULL,            'Home',                    0001,          NULL,                                  'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('AB655178-31C2-4541-BBC7-31AFFAB95A67', 'naviation_menu__jobs',                      NULL,            'Jobs',                    0002,          NULL,                                  'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('73813C5D-81C7-4BB3-B96D-CA324254B35A', 'naviation_menu__clients',                   NULL,            'Clients',                 0003,          NULL,                                  'Checkbox');
-- (OLD)                                'F649189D-978D-4C95-BABD-F0D761378644'  'Tools__Export'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'naviation_menu__export',                     [Description] = 'Export',    [SortOrder] = 0004
                           WHERE [Name] = 'Tools__Export'
-- (OLD)                                'C981CFBC-C1E7-4527-9F5D-05518094C22F'  'Tools__EnergyLab'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'naviation_menu__energylab',                  [Description] = 'EnergyLab', [SortOrder] = 0005
                           WHERE [Name] = 'Tools__EnergyLab'
INSERT INTO [dbo].[AspNetRoles] VALUES ('CB973CD1-7663-4455-8C6E-6F1FDE720C08', 'naviation_menu__admin',                     NULL,            'Admin',                   0006,          NULL,                                  'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('6158E39E-884C-4FCE-B8C3-C939E774D8DF', 'naviation_menu__settings',                  NULL,            'Settings',                0007,          NULL,                                  'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('DF6CADEB-11D1-40EB-8AAE-D2D6FF153630', 'naviation_menu__newjob',                    NULL,            'New Job',                 0008,          NULL,                                  'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('27C7AED1-438A-4596-8297-6FFA969AA0B0', 'naviation_menu__tools',                     NULL,            'Tools',                   0009,          NULL,                                  'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('4B084FDD-A530-420F-B1E5-88A7F85C8D56', 'naviation_menu__glazingcalculator',         NULL,            'Glazing Calculator',      0010,         '27C7AED1-438A-4596-8297-6FFA969AA0B0', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('1AABF6E1-17E9-4E29-ADCC-75B60D206385', 'naviation_menu__wholeofhome',               NULL,            'Whole-of-Home',           0011,         '27C7AED1-438A-4596-8297-6FFA969AA0B0', 'Checkbox');
-- --------------------- --
-- Home Page / Jobs Page --
-- --------------------- --
--                                      [Id]                                    [Name]                                                      [Discriminator], [Description]                                    [SortOrder]    [ParentRoleId]                          [Type]
INSERT INTO [dbo].[AspNetRoles] VALUES ('05AE3B6A-3B2D-45B5-AB9C-B1E6107EAC95', 'home_page_/_job_page__assignedself__assessor',             NULL,            'Jobs Assigned To Self',                         0101,          NULL,                                   'Disabled__Self');
INSERT INTO [dbo].[AspNetRoles] VALUES ('E4EEB086-79FC-4379-AC52-C35F481777CA', 'home_page_/_job_page__assignedself__client',               NULL,            'Jobs Assigned To Self',                         0102,          NULL,                                   'List__Client');
INSERT INTO [dbo].[AspNetRoles] VALUES ('73F151FB-87FA-4DCB-9C7B-34055111FE14', 'home_page_/_job_page__assignedself__status',               NULL,            'Jobs Assigned To Self',                         0103,          NULL,                                   'List__Status');
INSERT INTO [dbo].[AspNetRoles] VALUES ('B6ED4DBB-7CD5-49B0-A6DB-B99C56990C2B', 'home_page_/_job_page__assignedself__building_description', NULL,            'Jobs Assigned To Self',                         0104,          NULL,                                   'List__Building_Description');
INSERT INTO [dbo].[AspNetRoles] VALUES ('AF702BA8-4495-4274-A2EF-8E8166BC4FB0', 'home_page_/_job_page__assignedself__version',              NULL,            'Jobs Assigned To Self',                         0105,          NULL,                                   'List__Version');
-- (OLD)                                '465ABD5C-3FBA-43B8-9331-A517D0EEDE10'  'Assessment__Assigned_View_Self'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'home_page_/_job_page__assignedself__view',                  [Description] = 'Jobs Assigned To Self',           [SortOrder] = 0106
                           WHERE [Name] = 'Assessment__Assigned_View_Self'
INSERT INTO [dbo].[AspNetRoles] VALUES ('38F252F7-1283-47EB-A939-0B4ED3180D80', 'home_page_/_job_page__assignedself__access',               NULL,            'Jobs Assigned To Self',                         0107,          '465ABD5C-3FBA-43B8-9331-A517D0EEDE10', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('F867D08B-B76D-4EE9-B5BE-C294BB47BBD1', 'home_page_/_job_page__assignedother__assessor',            NULL,            'Jobs Assigned To Other Assessor',               0108,          NULL,                                   'List__Assessor');
INSERT INTO [dbo].[AspNetRoles] VALUES ('CAD90BD4-227A-45E0-B547-D3B76B30BD89', 'home_page_/_job_page__assignedother__client',              NULL,            'Jobs Assigned To Other Assessor',               0109,          NULL,                                   'List__Client');
INSERT INTO [dbo].[AspNetRoles] VALUES ('08445296-F9FB-4633-B949-A4C7D1EC7DC4', 'home_page_/_job_page__assignedother__status',              NULL,            'Jobs Assigned To Other Assessor',               0110,          NULL,                                   'List__Status');
INSERT INTO [dbo].[AspNetRoles] VALUES ('F32C2312-A0AA-45E9-82F4-674333E9D60C', 'home_page_/_job_page__assignedother__building_description', NULL,            'Jobs Assigned To Other Assessor',               0111,          NULL,                                   'List__Building_Description');
INSERT INTO [dbo].[AspNetRoles] VALUES ('41C94691-025A-4120-9196-BB11AAD70248', 'home_page_/_job_page__assignedother__version',             NULL,            'Jobs Assigned To Other Assessor',               0112,          NULL,                                   'List__Version');
-- (OLD)                                'EDBDB6AA-AE68-48D0-BA9D-FA0FB14BA90E'  'Assessment__Assigned_View_Other'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'home_page_/_job_page__assignedother__view',                  [Description] = 'Jobs Assigned To Other Assessor', [SortOrder] = 0113
                           WHERE [Name] = 'Assessment__Assigned_View_Other'

INSERT INTO [dbo].[AspNetRoles] VALUES ('A65B78C4-0858-4148-BE44-2FDAE940B37E', 'home_page_/_job_page__assignedother__access',              NULL,            'Jobs Assigned To Other Assessor',     0114,          'EDBDB6AA-AE68-48D0-BA9D-FA0FB14BA90E', 'Checkbox');
-- ------------------------------- --
-- Assessment Page (Tabs/Sub-Tabs) --
-- ------------------------------- --
--                                      [Id]                                    [Name]                                                                [Discriminator], [Description]                         [SortOrder]          [ParentRoleId]                          [Type]
-- (OLD)                                'D756A44A-79F8-44C7-836E-4D3AC270AD92'  'Assessment__Tabs_View_Assessment'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__assessment__view',                   [Description] = 'Assessment',        [SortOrder] =    0201
                           WHERE [Name] = 'Assessment__Tabs_View_Assessment'
-- (OLD)                                'B2A442BC-0624-4DC3-A6E6-9E8C0F280DC4'  'Assessment__Tabs_Edit_Assessment'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__assessment__edit',                   [Description] = 'Assessment',        [SortOrder] =    0202,  [ParentRoleId] = 'D756A44A-79F8-44C7-836E-4D3AC270AD92'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Assessment'
INSERT INTO [dbo].[AspNetRoles] VALUES ('93BC5B98-E9D3-4AC6-9B2E-879950B1F0A3', 'assessment_page_(tabs/sub-tabs)__creator__view',                     NULL,            'Creator',                            0203,                   'D756A44A-79F8-44C7-836E-4D3AC270AD92', 'Checkbox');
-- (OLD)                                '182CE7DD-772B-47D8-A6EE-2D05575FB9A3'  'Assessment__Inputs_Edit_Assessment_Creator'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__creator__edit',                      [Description] = 'Creator',           [SortOrder] =    0204,  [ParentRoleId] = '93BC5B98-E9D3-4AC6-9B2E-879950B1F0A3'
                           WHERE [Name] = 'Assessment__Inputs_Edit_Assessment_Creator'
INSERT INTO [dbo].[AspNetRoles] VALUES ('B70D4AD1-FAC7-45D3-953E-84F93EBE6857', 'assessment_page_(tabs/sub-tabs)__assignee__view',                    NULL,            'Assignee',                           0205,                   'D756A44A-79F8-44C7-836E-4D3AC270AD92', 'Checkbox');
-- (OLD)                                'CB43D035-8AA3-4803-9913-91E86E4D3A37'  'Assessment__Inputs_Edit_Assessment_Assignee'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__assignee__edit',                     [Description] = 'Assignee',          [SortOrder] =    0206,  [ParentRoleId] = 'B70D4AD1-FAC7-45D3-953E-84F93EBE6857'
                           WHERE [Name] = 'Assessment__Inputs_Edit_Assessment_Assignee'
INSERT INTO [dbo].[AspNetRoles] VALUES ('5158B421-8991-4181-A3EE-55BC4EC661DA', 'assessment_page_(tabs/sub-tabs)__assignedassessor__view',            NULL,            'Assigned Assessor',                  0207,                   'D756A44A-79F8-44C7-836E-4D3AC270AD92', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('BCC4EFED-485F-464C-A407-B576A9B5CF8D', 'assessment_page_(tabs/sub-tabs)__assignedassessor__edit',            NULL,            'Assigned Assessor',                  0208,                   '5158B421-8991-4181-A3EE-55BC4EC661DA', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('487843AF-F08E-45B4-8A91-71F6819591EE', 'assessment_page_(tabs/sub-tabs)__eventhistory__view',                NULL,            'Event History',                      0209,                   'D756A44A-79F8-44C7-836E-4D3AC270AD92', 'Checkbox');
-- (OLD)                                '50EF8856-FDA4-46F7-903B-E1F5CD99317C'  'Assessment__Tabs_View_Invoicing'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__invoicing__view',                    [Description] = 'Invoicing',         [SortOrder] =    0210
                           WHERE [Name] = 'Assessment__Tabs_View_Invoicing'
-- (OLD)                                '38F01E2E-7E6E-4FAE-A8F1-CC26620041EE'  'Assessment__Tabs_Edit_Invoicing'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__invoicing__edit',                    [Description] = 'Invoicing',         [SortOrder] =    0211, [ParentRoleId] = '50EF8856-FDA4-46F7-903B-E1F5CD99317C'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Invoicing'
-- (OLD)                                '08451780-047F-4379-812F-B46A4B38DA75'  'Assessment__Tabs_View_Site'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__site__view',                         [Description] = 'Site',              [SortOrder] =    0212
                           WHERE [Name] = 'Assessment__Tabs_View_Site'
-- (OLD)                                '77C4F503-479E-49C4-AA1D-CB8BF7B7EA4D'  'Assessment__Tabs_Edit_Site'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__site__edit',                         [Description] = 'Site',              [SortOrder] =    0213, [ParentRoleId] = '08451780-047F-4379-812F-B46A4B38DA75'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Site'
-- (OLD)                                'DEDAD32C-746B-455E-AB43-F3341E75F1E1'  'Assessment__Tabs_View_Address'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__address__view',                      [Description] = 'Address',           [SortOrder] =    0214, [ParentRoleId] = '08451780-047F-4379-812F-B46A4B38DA75'
                           WHERE [Name] = 'Assessment__Tabs_View_Address'
-- (OLD)                                '942B8430-3FB1-4034-AC89-0D64E5FBFE69'  'Assessment__Tabs_Edit_Address'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__address__edit',                      [Description] = 'Address',           [SortOrder] =    0215, [ParentRoleId] = 'DEDAD32C-746B-455E-AB43-F3341E75F1E1'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Address'
INSERT INTO [dbo].[AspNetRoles] VALUES ('29014E50-391C-4D6F-A62D-E0A2A384C821', 'assessment_page_(tabs/sub-tabs)__searchaddress__view',               NULL,            'Search Address',                     0216,                  'DEDAD32C-746B-455E-AB43-F3341E75F1E1', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('8BAD21F8-9563-45EC-ACC8-1D4A9FC9A64B', 'assessment_page_(tabs/sub-tabs)__searchaddress__edit',               NULL,            'Search Address',                     0217,                  '29014E50-391C-4D6F-A62D-E0A2A384C821', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('B14B19ED-C6C9-4B89-B96A-E59AE780E137', 'assessment_page_(tabs/sub-tabs)__customaddress__view',               NULL,            'Custom Project Address (Check Box)', 0218,                  'DEDAD32C-746B-455E-AB43-F3341E75F1E1', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('02B227B2-FFE7-4689-9A57-EC828E3CA9CC', 'assessment_page_(tabs/sub-tabs)__customaddress__edit',               NULL,            'Custom Project Address (Check Box)', 0219,                  'B14B19ED-C6C9-4B89-B96A-E59AE780E137', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('CEA4C9C5-BE5D-4493-A1B0-ECDA2AA8BE30', 'assessment_page_(tabs/sub-tabs)__projectowner__view',                NULL,            'Project Owner',                      0220,                  'DEDAD32C-746B-455E-AB43-F3341E75F1E1', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('F4A78034-1761-46EF-8192-B5CC88BAFEA7', 'assessment_page_(tabs/sub-tabs)__projectowner__edit',                NULL,            'Project Owner',                      0221,                  'CEA4C9C5-BE5D-4493-A1B0-ECDA2AA8BE30', 'Checkbox');
-- (OLD)                                '064CBEBC-CA34-40DA-942C-420EB1AE41AB'  'Assessment__Tabs_View_Map'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__map__view',                          [Description] = 'Map',               [SortOrder] =    0222, [ParentRoleId] = '08451780-047F-4379-812F-B46A4B38DA75'
                           WHERE [Name] = 'Assessment__Tabs_View_Map'
-- (OLD)                                '2E07CDF4-4BE2-4A47-BC9C-A6FC5325AC27'  'Assessment__Tabs_Edit_Map'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__map__edit',                          [Description] = 'Map',               [SortOrder] =    0223, [ParentRoleId] = '064CBEBC-CA34-40DA-942C-420EB1AE41AB'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Map'
-- (OLD)                                '44A3C032-73C7-4E8A-9535-B49CE5D8685C'  'Assessment__Tabs_View_AerialImage'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__aerialimage__view',                  [Description] = 'Aerial Image',      [SortOrder] =    0224, [ParentRoleId] = '08451780-047F-4379-812F-B46A4B38DA75'
                           WHERE [Name] = 'Assessment__Tabs_View_AerialImage'
-- (OLD)                                '16F0A525-A1E6-4EE7-952B-F2B843DF18A8'  'Assessment__Tabs_Edit_AerialImage'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__aerialimage__edit',                  [Description] = 'Aerial Image',      [SortOrder] =    0225, [ParentRoleId] = '44A3C032-73C7-4E8A-9535-B49CE5D8685C'
                           WHERE [Name] = 'Assessment__Tabs_Edit_AerialImage'
-- (OLD)                                'DDFD1100-84E4-4303-8037-B719D55FFA71'  'Assessment__Tabs_View_Climate'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__climate__view',                      [Description] = 'Climate',           [SortOrder] =    0226, [ParentRoleId] = '08451780-047F-4379-812F-B46A4B38DA75'
                           WHERE [Name] = 'Assessment__Tabs_View_Climate'
-- (OLD)                                'ED499125-8CE7-4B31-B30E-4C146CE93E48'  'Assessment__Tabs_Edit_Climate'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__climate__edit',                      [Description] = 'Climate',           [SortOrder] =    0227, [ParentRoleId] = 'DDFD1100-84E4-4303-8037-B719D55FFA71'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Climate'
-- (OLD)                                '5B46856D-149E-4B20-8BC6-34C30A9C98EC'  'Assessment__Tabs_View_Lot'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__lot__view',                          [Description] = 'Lot',               [SortOrder] =    0228, [ParentRoleId] = '08451780-047F-4379-812F-B46A4B38DA75'
                           WHERE [Name] = 'Assessment__Tabs_View_Lot'
-- (OLD)                                '0F5F0EC1-A768-4B6B-8570-FE65B256E882'  'Assessment__Tabs_Edit_Lot'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__lot__edit',                          [Description] = 'Lot',               [SortOrder] =    0229, [ParentRoleId] = '5B46856D-149E-4B20-8BC6-34C30A9C98EC'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Lot'
-- (OLD)                                '34BD7FBB-4305-4062-A60E-3DFD59DF4665'  'Assessment__Tabs_View_General'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__general__view',                      [Description] = 'General',           [SortOrder] =    0230
                           WHERE [Name] = 'Assessment__Tabs_View_General'
-- (OLD)                                'AF5395AC-299F-4CE3-9EE1-51968ED13F0E'  'Assessment__Tabs_Edit_General'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__general__edit',                      [Description] = 'General',           [SortOrder] =    0231, [ParentRoleId] = '34BD7FBB-4305-4062-A60E-3DFD59DF4665'
                           WHERE [Name] = 'Assessment__Tabs_Edit_General'
-- (OLD)                                'F1D7CDD8-B320-436B-AD61-1455B92ACD11'  'Assessment__Tabs_View_Design'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__design__view',                       [Description] = 'Design',            [SortOrder] =    0232
                           WHERE [Name] = 'Assessment__Tabs_View_Design'
-- (OLD)                                'E43357DE-7439-4F6F-B029-C9FE31E79755'  'Assessment__Tabs_Edit_Design'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__design__edit',                       [Description] = 'Design',            [SortOrder] =    0233, [ParentRoleId] = 'F1D7CDD8-B320-436B-AD61-1455B92ACD11'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Design'
-- (OLD)                                '957972D0-15BA-4781-94CF-B03E18123E30'  'Assessment__Tabs_View_Construction'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__construction__view',                 [Description] = 'Construction',      [SortOrder] =    0234
                           WHERE [Name] = 'Assessment__Tabs_View_Construction'
-- (OLD)                                '6CE930C9-FC92-4518-9423-57A8B27E1594'  'Assessment__Tabs_Edit_Construction'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__construction__edit',                 [Description] = 'Construction',      [SortOrder] =    0235, [ParentRoleId] = '957972D0-15BA-4781-94CF-B03E18123E30'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Construction'
-- (OLD)                                '881EB436-7BFC-4625-9214-75CC16564D62'  'Assessment__Tabs_View_Openings'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__openings__view',                     [Description] = 'Openings',          [SortOrder] =    0236
                           WHERE [Name] = 'Assessment__Tabs_View_Openings'
-- (OLD)                                'C2169E30-C4D9-4216-A79E-E743B0407DBA'  'Assessment__Tabs_Edit_Openings'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__openings__edit',                     [Description] = 'Openings',          [SortOrder] =    0237, [ParentRoleId] = '881EB436-7BFC-4625-9214-75CC16564D62'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Openings'
-- (OLD)                                'E581E2E1-4951-4E80-A935-218785DDCD74'  'Assessment__Tabs_View_Services'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__services__view',                     [Description] = 'Services',          [SortOrder] =    0238
                           WHERE [Name] = 'Assessment__Tabs_View_Services'
-- (OLD)                                'B1C7B319-41AC-45F2-9413-2A35A72E4D14'  'Assessment__Tabs_Edit_Services'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__services__edit',                     [Description] = 'Services',          [SortOrder] =    0239, [ParentRoleId] = 'E581E2E1-4951-4E80-A935-218785DDCD74'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Services'
-- (OLD)                                '445B20FB-FEF8-4685-8860-5E249885295A'  'Assessment__Tabs_View_Drawings'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__drawings__view',                     [Description] = 'Drawings',          [SortOrder] =    0240
                           WHERE [Name] = 'Assessment__Tabs_View_Drawings'
-- (OLD)                                '80449E27-F6AE-419E-A7AF-0B171CBD8258'  'Assessment__Tabs_Edit_Drawings'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__drawings__edit',                     [Description] = 'Drawings',          [SortOrder] =    0241, [ParentRoleId] = '445B20FB-FEF8-4685-8860-5E249885295A'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Drawings'
-- (OLD)                                '62C8B051-64CB-457F-9DAA-DD543C45C59C'  'Assessment__Tabs_View_Simulation'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__simulation__view',                   [Description] = 'Simulation',        [SortOrder] =    0242
                           WHERE [Name] = 'Assessment__Tabs_View_Simulation'
-- (OLD)                                '10424C2C-F797-4EC8-9F1C-336ED3C72CA8'  'Assessment__Tabs_Edit_Simulation'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__simulation__edit',                   [Description] = 'Simulation',        [SortOrder] =    0243, [ParentRoleId] = '62C8B051-64CB-457F-9DAA-DD543C45C59C'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Simulation'
INSERT INTO [dbo].[AspNetRoles] VALUES ('7AF3076F-F174-4DAE-964F-75BBA773CF6C', 'assessment_page_(tabs/sub-tabs)__certification__view',               NULL,            'Certification',                      0244,                  '62C8B051-64CB-457F-9DAA-DD543C45C59C', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('C157D962-9E1F-4518-895D-1B97A5AE487F', 'assessment_page_(tabs/sub-tabs)__certification__edit',               NULL,            'Certification',                      0245,                  '7AF3076F-F174-4DAE-964F-75BBA773CF6C', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('2DF1B470-60AB-4ECE-8FE7-660DE9702908', 'assessment_page_(tabs/sub-tabs)__assessmentmethod__view',            NULL,            'Assessment Method',                  0246,                  '62C8B051-64CB-457F-9DAA-DD543C45C59C', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('7FC1692B-7F4C-4E4A-A022-7C9346F5BABB', 'assessment_page_(tabs/sub-tabs)__assessmentmethod__edit',            NULL,            'Assessment Method',                  0247,                  '2DF1B470-60AB-4ECE-8FE7-660DE9702908', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('6AB99C8A-D49C-4FF7-9CA8-278BE8F892DF', 'assessment_page_(tabs/sub-tabs)__assessmentsoftware__view',          NULL,            'Assessment Software',                0248,                  '62C8B051-64CB-457F-9DAA-DD543C45C59C', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('C6946BB8-3F20-4525-9FD9-134F3BC78FC5', 'assessment_page_(tabs/sub-tabs)__assessmentsoftware__edit',          NULL,            'Assessment Software',                0249,                  '6AB99C8A-D49C-4FF7-9CA8-278BE8F892DF', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('02821CAC-5150-4CCF-AC04-849E42F27216', 'assessment_page_(tabs/sub-tabs)__herrequired__view',                 NULL,            'House Energy Rating - Required',     0250,                  '62C8B051-64CB-457F-9DAA-DD543C45C59C', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('0512CD9B-811C-4BE3-B369-1321699C9FD7', 'assessment_page_(tabs/sub-tabs)__herrequired__edit',                 NULL,            'House Energy Rating - Required',     0251,                  '02821CAC-5150-4CCF-AC04-849E42F27216', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('7DCC7FA1-1925-4084-87C9-BCCBC1150A9C', 'assessment_page_(tabs/sub-tabs)__multisim__view',                    NULL,            'Multi-Sim',                          0252,                  NULL,                                   'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('1532FF37-5C6A-44E9-BBFB-7C585CBC5E8C', 'assessment_page_(tabs/sub-tabs)__multisim__edit',                    NULL,            'Multi-Sim',                          0253,                  '7DCC7FA1-1925-4084-87C9-BCCBC1150A9C', 'Checkbox');
-- (OLD)                                'D82E29D7-772B-4EBE-8593-429AF795D0EE'  'Assessment__Tabs_View_Analytics'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__analytics__view',                    [Description] = 'Analytics',         [SortOrder] =    0254
                           WHERE [Name] = 'Assessment__Tabs_View_Analytics'
-- (OLD)                                'C005BECD-0E6F-45C4-A7AC-338316002C42'  'Assessment__Tabs_Edit_Analytics'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__analytics__edit',                    [Description] = 'Analytics',         [SortOrder] =    0255, [ParentRoleId] = 'D82E29D7-772B-4EBE-8593-429AF795D0EE'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Analytics'
-- (OLD)                                'E0222352-96AA-45F6-BCD9-8BA5DDDB537E'  'Assessment__Tabs_View_Results'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__results__view',                      [Description] = 'Results',           [SortOrder] =    0256
                           WHERE [Name] = 'Assessment__Tabs_View_Results'
-- (OLD)                                '661478C0-27F8-4493-A5F8-F6C70F87FAD7'  'Assessment__Tabs_Edit_Results'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__results__edit',                      [Description] = 'Results',           [SortOrder] =    0257, [ParentRoleId] = 'E0222352-96AA-45F6-BCD9-8BA5DDDB537E'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Results'
-- (OLD)                                '05CFA092-4044-47A6-A130-950A8B17D8A2'  'Assessment__Tabs_View_Reports'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__reports__view',                      [Description] = 'Reports',           [SortOrder] =    0258
                           WHERE [Name] = 'Assessment__Tabs_View_Reports'
-- (OLD)                                'D0C6C2C7-A52C-4C10-B63A-4EB551861E51'  'Assessment__Tabs_Edit_Reports'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_page_(tabs/sub-tabs)__reports__edit',                      [Description] = 'Reports',           [SortOrder] =    0259, [ParentRoleId] = '05CFA092-4044-47A6-A130-950A8B17D8A2'
                           WHERE [Name] = 'Assessment__Tabs_Edit_Reports'
-- ------------------ --
-- Assessment Actions --
-- ------------------ --
--                                      [Id]                                    [Name]                                                   [Discriminator], [Description]                              [SortOrder] [ParentRoleId] [Type]
INSERT INTO [dbo].[AspNetRoles] VALUES ('92D0AA27-5BB9-419F-997E-D00F438621CD', 'assessment_actions__viewpreviousversions',              NULL,            'View Previous Versions',                  0301,          NULL,          'Checkbox');
-- (OLD)                                '0EC4D3E6-B755-4017-BDF9-7F5A7ABCC6E7'                   'Actions__Assessment_Copy'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__copyassessment',                     [Description] = 'Copy Assessment',           [SortOrder] = 0302
                           WHERE [Name] = 'Actions__Assessment_Copy'
-- (OLD)                                '062181D9-4416-47BE-8F53-617F11BD1FB0'                   'Actions__Assessment_Edit'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__editassessment',                     [Description] = 'Edit Assessment',           [SortOrder] = 0303
                           WHERE [Name] = 'Actions__Assessment_Edit'
-- (OLD)                                '693AB24D-433C-41B8-8CBF-1F785CAFF700'                   'Actions__Assessment_Cancel'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__cancelassessment',                   [Description] = 'Cancel Assessment',         [SortOrder] = 0304
                           WHERE [Name] = 'Actions__Assessment_Cancel'
-- (OLD)                                'D1EE854C-7554-445C-AE14-C12C1B15099C'                   'Actions__Assessment_Delete'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__deleteassessment',                   [Description] = 'Delete Assessment',         [SortOrder] = 0305
                           WHERE [Name] = 'Actions__Assessment_Delete'
-- (OLD)                                '2AAF978B-A683-4642-995B-FA186F1DCFE7'                   'Actions__Assessment_Archive'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__archiveassessment',                  [Description] = 'Archive Assessment',        [SortOrder] = 0306
                           WHERE [Name] = 'Actions__Assessment_Archive'
-- (OLD)                                '98916473-B834-40C7-BE8C-8E80C3926C98'                   'Tools__GlazingCalculator'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__glazingcalculator',                  [Description] = 'Export Glazing Calculator', [SortOrder] = 0307
                           WHERE [Name] = 'Tools__GlazingCalculator'
-- (OLD)                                '2B9DFCF3-61B1-4ED4-8167-E4C9D7EB0F38'                   'Tools__WholeOfHomeCalculator'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__exportwoh',                          [Description] = 'Export Whole of Home',      [SortOrder] = 0308
                           WHERE [Name] = 'Tools__WholeOfHomeCalculator'
INSERT INTO [dbo].[AspNetRoles] VALUES ('01795C3B-511B-4C2B-BD82-C61273BFB6BA', 'assessment_actions__addmultisim',                       NULL,            'Add Multi-Sim Option',                    0309,          NULL,          'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('C5339ECE-D916-4A43-AC14-6A0B9A499E79', 'assessment_actions__deletemultisim',                    NULL,            'Delete Multi-Sim Option',                 0310,         NULL,          'Checkbox');
-- (OLD)                                'FDBD6CFB-2095-4753-9102-487FCA2A91A8'                   'Actions__Compliance_Create'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__addcompliance',                      [Description] = 'Add Compliance Option',     [SortOrder] = 0311
                           WHERE [Name] = 'Actions__Compliance_Create'
-- (OLD)                                'DC62CDC1-5863-4940-8FC0-A94C7F1FFE4E'                   'Actions__Compliance_Delete'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__deletecompliance',                   [Description] = 'Delete Compliance Option',  [SortOrder] = 0312
                           WHERE [Name] = 'Actions__Compliance_Delete'
-- (OLD)                                '2A780300-7F5C-4FC9-8311-0BD974643EEC'                   'Actions__Compliance_OptionSelect'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__selectcompliance',                   [Description] = 'Select Compliance Option',  [SortOrder] = 0313
                           WHERE [Name] = 'Actions__Compliance_OptionSelect'
-- (OLD)                                'E40FA481-C7F6-4ED1-9A99-A8A1DA6F6B20'                   'Actions__Assessment_ApproveComplianceOptions'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__approvecompliance',                  [Description] = 'Approve Compliance Option', [SortOrder] = 0314
                           WHERE [Name] = 'Actions__Assessment_ApproveComplianceOptions'
-- (OLD)                                '5002549C-6C0C-4A01-883B-590DF1162A49'                   'Actions__Assessment_Issue'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__finalisereport',                     [Description] = 'Finalise Report',           [SortOrder] = 0315
                           WHERE [Name] = 'Actions__Assessment_Issue'
-- (OLD)                                '336EA275-D017-48D2-9005-8412F07BE32E'                   'Actions__Assessment_Recertify'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'assessment_actions__recertify',                          [Description] = 'Recertify',                 [SortOrder] = 0316
                           WHERE [Name] = 'Actions__Assessment_Recertify'
-- ----- --
-- Admin --
-- ----- --
--                                      [Id]                                    [Name]                         [Description]                                   [SortOrder]            [ParentRoleId]
-- (OLD)                                'AE3595EB-C823-4E81-B97B-E9BE810612D5'  'Admin__Admin_Audit'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__adminaudit',           [Description] = 'Admin Audit',    [SortOrder] = 0401
                           WHERE [Name] = 'Admin__Admin_Audit'
-- (OLD)                                'D3570B98-3092-44F9-890C-96153481850A'  'Admin__BusinessUnit_View'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__businessunit__view',   [Description] = 'Business Unit',  [SortOrder] = 0402
                           WHERE [Name] = 'Admin__BusinessUnit_View'
-- (OLD)                                '38659CB7-13E7-4848-8118-BC52F7D24EFA'  'Admin__BusinessUnit_Edit'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__businessunit__edit',   [Description] = 'Business Unit',  [SortOrder] = 0403, [ParentRoleId] = 'D3570B98-3092-44F9-890C-96153481850A'
                           WHERE [Name] = 'Admin__BusinessUnit_Edit'
-- (OLD)                                '7E7572B2-7E05-443D-8E52-C1B96B03E045'  'Admin__BusinessUnit_Create'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__businessunit__create', [Description] = 'Business Unit',  [SortOrder] = 0404, [ParentRoleId] = 'D3570B98-3092-44F9-890C-96153481850A'
                           WHERE [Name] = 'Admin__BusinessUnit_Create'
-- (OLD)                                '97275FB8-A502-4E1C-B326-7F957774933A'  'Admin__BusinessUnit_Delete'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__businessunit__delete', [Description] = 'Business Unit',  [SortOrder] = 0405, [ParentRoleId] = 'D3570B98-3092-44F9-890C-96153481850A'
                           WHERE [Name] = 'Admin__BusinessUnit_Delete'
-- (OLD)                                '65CC15F7-7716-4BA5-9C46-6B00FE12D2AD'  'Admin__Client_View'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__client__view',         [Description] = 'Client',      [SortOrder] = 0406, [ParentRoleId] = '73813C5D-81C7-4BB3-B96D-CA324254B35A'
                           WHERE [Name] = 'Admin__Client_View'
-- (OLD)                                '44AC4F28-3AE1-4106-A997-4160E24C60DE'  'Admin__Client_Edit'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__client__edit',         [Description] = 'Client',      [SortOrder] = 0407, [ParentRoleId] = '65CC15F7-7716-4BA5-9C46-6B00FE12D2AD'
                           WHERE [Name] = 'Admin__Client_Edit'
-- (OLD)                                '40DAED6D-EF37-4649-A800-AC307C665554'  'Admin__Client_Create'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__client__create',       [Description] = 'Client',      [SortOrder] = 0408, [ParentRoleId] = '65CC15F7-7716-4BA5-9C46-6B00FE12D2AD'
                           WHERE [Name] = 'Admin__Client_Create'
-- (OLD)                                '2B113C3D-680D-492C-BCA4-0D86ACBEC50B'  'Admin__Client_Delete'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__client__delete',       [Description] = 'Client',      [SortOrder] = 0409, [ParentRoleId] = '65CC15F7-7716-4BA5-9C46-6B00FE12D2AD'
                           WHERE [Name] = 'Admin__Client_Delete'
-- (OLD)                                'A0F88DE6-1095-4D59-AE8F-13B026B6F9B6'  'Admin__Template_View'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__template__view',       [Description] = 'Template',    [SortOrder] = 0410
                           WHERE [Name] = 'Admin__Template_View'
-- (OLD)                                'D340ABBE-BCBE-450D-AA91-BD3BC3B19861'  'Admin__Template_Edit'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__template__edit',       [Description] = 'Template',    [SortOrder] = 0411, [ParentRoleId] = 'A0F88DE6-1095-4D59-AE8F-13B026B6F9B6'
                           WHERE [Name] = 'Admin__Template_Edit'
-- (OLD)                                '5ADDD710-7BDE-49D0-8EDE-42A81878454E'  'Admin__Template_Create'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__template__create',     [Description] = 'Template',    [SortOrder] = 0412, [ParentRoleId] = 'A0F88DE6-1095-4D59-AE8F-13B026B6F9B6'
                           WHERE [Name] = 'Admin__Template_Create'
-- (OLD)                                'DAC14050-6215-4229-8A76-AAC78AD0ADB0'  'Admin__Template_Delete'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__template__delete',     [Description] = 'Template',    [SortOrder] = 0413, [ParentRoleId] = 'A0F88DE6-1095-4D59-AE8F-13B026B6F9B6'
                           WHERE [Name] = 'Admin__Template_Delete'
-- (OLD)                                '3DEED746-5AA3-4636-B875-202172CC01FD'  'Admin__User_View'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__users__view',          [Description] = 'Users',       [SortOrder] = 0414
                           WHERE [Name] = 'Admin__User_View'
-- (OLD)                                'A1A4D242-C6A7-46BA-8CCB-AB957137BCC6'  'Admin__User_Edit'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__users__edit',          [Description] = 'Users',       [SortOrder] = 0415, [ParentRoleId] = '3DEED746-5AA3-4636-B875-202172CC01FD'
                           WHERE [Name] = 'Admin__User_Edit'
-- (OLD)                                '252DD1E7-51FE-4CE2-9B5B-1B8B5CC7A682'  'Admin__User_Create'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__users__create',        [Description] = 'Users',       [SortOrder] = 0416, [ParentRoleId] = '3DEED746-5AA3-4636-B875-202172CC01FD'
                           WHERE [Name] = 'Admin__User_Create'
-- (OLD)                                '92DD837C-C4EB-47CE-9BA4-8DEBFA7ED204'  'Admin__User_Delete'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__users__delete',        [Description] = 'Users',       [SortOrder] = 0417, [ParentRoleId] = '3DEED746-5AA3-4636-B875-202172CC01FD'
                           WHERE [Name] = 'Admin__User_Delete'
-- (OLD)                                'AD02B48C-9369-4C84-93C5-61C8E96B0638'  'Admin__User_Audit'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'admin__useraudit__view',      [Description] = 'User Audit',  [SortOrder] = 0418
                           WHERE [Name] = 'Admin__User_Audit'
-- -------- --
-- Settings --
-- -------- --
--                                      [Id]                                    [Name]                            [Description]                                        [SortOrder]            [ParentRoleId]
-- (OLD)                                '1F43DBD8-991E-4AE6-8A1E-CCDB61E96F71'  'Settings__View'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'settings__settings__view',       [Description] = 'Settings',            [SortOrder] = 0501, [ParentRoleId] = '6158E39E-884C-4FCE-B8C3-C939E774D8DF'
                           WHERE [Name] = 'Settings__View'
-- (OLD)                                '138B47B4-53D8-40D6-8CA0-B0889B18374C'  'Settings__Edit'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'settings__settings__edit',       [Description] = 'Settings',             [SortOrder] = 0502, [ParentRoleId] = '1F43DBD8-991E-4AE6-8A1E-CCDB61E96F71'
                           WHERE [Name] = 'Settings__Edit'
-- (OLD)                                '39B49404-498A-40A5-B9BD-F7B751450ABD'  'Settings__Create'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'settings__settings__create',     [Description] = 'Settings',             [SortOrder] = 0503, [ParentRoleId] = '1F43DBD8-991E-4AE6-8A1E-CCDB61E96F71'
                           WHERE [Name] = 'Settings__Create'
-- (OLD)                                '2BE035C3-4947-4305-9ACC-D679CF97C833'  'Settings__Delete'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'settings__settings__delete',     [Description] = 'Settings',             [SortOrder] = 0504, [ParentRoleId] = '1F43DBD8-991E-4AE6-8A1E-CCDB61E96F71'
                           WHERE [Name] = 'Settings__Delete'
-- (OLD)                                'CDAF87C4-5EEC-46C4-B391-84F8947EBEAD'  'Settings__ViewSystemLog'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'settings__systemlog__view',      [Description] = 'System Log',           [SortOrder] = 0505, [ParentRoleId] = '6158E39E-884C-4FCE-B8C3-C939E774D8DF'
                           WHERE [Name] = 'Settings__ViewSystemLog'
-- (OLD)                                'BFE4DBDB-697B-417B-869A-7A8AA0CA4325'  'Settings__UploadDataSets'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'settings__uploaddatasets__view', [Description] = 'Upload Data Sets',     [SortOrder] = 0506, [ParentRoleId] = '6158E39E-884C-4FCE-B8C3-C939E774D8DF'
                           WHERE [Name] = 'Settings__UploadDataSets'
-- ------------- --
-- Client Portal --
-- ------------- --
--                                      [Id]                                    [Name]                                               [Discriminator], [Description]                              [SortOrder] [ParentRoleId]                          [Type]
-- (OLD)                                '3F475D54-48A8-4F69-8A96-46C00FD3889D'  'ClientPortal'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'client_portal__clientportalaccess',                  [Description] = 'Client Portal Access',   [SortOrder] =    0601
                           WHERE [Name] = 'ClientPortal'
-- (OLD)                                'C1CD4B8E-5CE5-495C-906F-FAC8144A732A'  'ClientPortalLoginAs'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'client_portal__clientportalloginas',                 [Description] = 'Client Portal Login As', [SortOrder] =    0602
                           WHERE [Name] = 'ClientPortalLoginAs'
INSERT INTO [dbo].[AspNetRoles] VALUES ('E785A91B-B76A-4728-B2EF-EDF61F95A7B4', 'client_portal__loginasclients',                     NULL,            'Login As Clients',                        0603,       'C1CD4B8E-5CE5-495C-906F-FAC8144A732A', 'List__Client');
-- (OLD)                                '6B656F09-A1F6-4020-A0AE-C047AC87DC84'  'ViewComplianceCost'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'client_portal__viewcompliancecost',                  [Description] = 'View Compliance Cost',   [SortOrder] =    0604
                           WHERE [Name] = 'ViewComplianceCost'
-- (OLD)                                'E9A75AAF-2EC3-4469-B003-2960F9F43BE1'  'EditComplianceCost'
UPDATE [dbo].[AspNetRoles]      SET                                    [Name] = 'client_portal__editcompliancecost',                  [Description] = 'Edit Compliance Cost',   [SortOrder] =    0605
                           WHERE [Name] = 'EditComplianceCost'
-- --------------------------- --
-- DELETE OLD NOT USED ANYMORE --
-- --------------------------- --
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = 'FA9B0ACC-E766-4241-89A5-BFD0A04841DC'; -- Actions__Assessment_EditLocked
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = 'FA9B0ACC-E766-4241-89A5-BFD0A04841DC';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = 'FA9B0ACC-E766-4241-89A5-BFD0A04841DC';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '13A29B4B-8444-41D2-88C7-BDA3EF8585F3'; -- Actions__Job_Create
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '13A29B4B-8444-41D2-88C7-BDA3EF8585F3';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '13A29B4B-8444-41D2-88C7-BDA3EF8585F3';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '5BF7B78F-4B6C-4C9E-8050-596CD099A390'; -- Assessment__Assigned_Edit_Other
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '5BF7B78F-4B6C-4C9E-8050-596CD099A390';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '5BF7B78F-4B6C-4C9E-8050-596CD099A390';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '70C67730-8E78-47EF-B4AC-69C9E16BBDDE'; -- Assessment__Assigned_Edit_Self
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '70C67730-8E78-47EF-B4AC-69C9E16BBDDE';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '70C67730-8E78-47EF-B4AC-69C9E16BBDDE';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '8AF90255-3128-49C6-9E8E-225320D3B469'; -- Assessment__Status_ACancelled
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '8AF90255-3128-49C6-9E8E-225320D3B469';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '8AF90255-3128-49C6-9E8E-225320D3B469';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '9AD7ECBB-4AE3-4675-BC65-83989494F12A'; -- Assessment__Status_AComplete
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '9AD7ECBB-4AE3-4675-BC65-83989494F12A';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '9AD7ECBB-4AE3-4675-BC65-83989494F12A';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '69023696-A0AD-472F-A6A4-B878881057CE'; -- Assessment__Status_ACompliance
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '69023696-A0AD-472F-A6A4-B878881057CE';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '69023696-A0AD-472F-A6A4-B878881057CE';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '1B226C74-5354-41D6-82FA-CB1EBE06EF2D'; -- Assessment__Status_ADraft
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '1B226C74-5354-41D6-82FA-CB1EBE06EF2D';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '1B226C74-5354-41D6-82FA-CB1EBE06EF2D';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '05631851-6F8F-4A98-B3FD-65D74B2A2F49'; -- Assessment__Status_AInProgress
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '05631851-6F8F-4A98-B3FD-65D74B2A2F49';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '05631851-6F8F-4A98-B3FD-65D74B2A2F49';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '2462EDF9-168F-43CE-A1D2-6BC1D01C40CC'; -- Assessment__Status_AIssued
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '2462EDF9-168F-43CE-A1D2-6BC1D01C40CC';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '2462EDF9-168F-43CE-A1D2-6BC1D01C40CC';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = 'C93BDADA-44DA-4EDE-AA7B-1C2997F27CFE'; -- Assessment__Status_AOptionSelected
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = 'C93BDADA-44DA-4EDE-AA7B-1C2997F27CFE';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = 'C93BDADA-44DA-4EDE-AA7B-1C2997F27CFE';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = 'CF558ECD-3540-4A99-A469-1531734C27A7'; -- Assessment__Status_APreliminaryReview
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = 'CF558ECD-3540-4A99-A469-1531734C27A7';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = 'CF558ECD-3540-4A99-A469-1531734C27A7';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = 'C0C7E254-1CDD-43CD-8CD7-91304ECF38D6'; -- Assessment__Status_ARecertification
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = 'C0C7E254-1CDD-43CD-8CD7-91304ECF38D6';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = 'C0C7E254-1CDD-43CD-8CD7-91304ECF38D6';
DELETE FROM [dbo].[RSS_RolesBusinessRole] WHERE [aspnet_RoleId] = '8959F2B7-2341-4266-A3D3-7CD58ACD4625'; -- Assessment__Status_ASuperseded
DELETE FROM [dbo].[AspNetUserRoles]       WHERE [RoleId]        = '8959F2B7-2341-4266-A3D3-7CD58ACD4625';
DELETE FROM [dbo].[AspNetRoles]           WHERE [Id]            = '8959F2B7-2341-4266-A3D3-7CD58ACD4625';




--changeset:170325
INSERT INTO [dbo].[AspNetRoles]
([Id],                                  [Name],                                     [Description],           [SortOrder], [ParentRoleId], [Type])
VALUES
('6F668396-E2BD-47C2-A3D5-FB80D9C12B92', 'assessment_actions__overrideenergyloads', 'Override Energy Loads', 0311,        NULL,           'Checkbox');
UPDATE [dbo].[AspNetRoles] SET [SortOrder] = 0312 WHERE [Name] = 'assessment_actions__addcompliance';
UPDATE [dbo].[AspNetRoles] SET [SortOrder] = 0313 WHERE [Name] = 'assessment_actions__deletecompliance';
UPDATE [dbo].[AspNetRoles] SET [SortOrder] = 0314 WHERE [Name] = 'assessment_actions__selectcompliance';
UPDATE [dbo].[AspNetRoles] SET [SortOrder] = 0315 WHERE [Name] = 'assessment_actions__approvecompliance';
UPDATE [dbo].[AspNetRoles] SET [SortOrder] = 0316 WHERE [Name] = 'assessment_actions__finalisereport';
UPDATE [dbo].[AspNetRoles] SET [SortOrder] = 0317 WHERE [Name] = 'assessment_actions__recertify';


--changeset JCC:100425
    -- Add Audio Visual feature option
ALTER TABLE [dbo].[RSS_StandardHomeModel] ADD [FeaturesAudioVisual] BIT NULL


--changeset JCC:180425
ALTER TABLE [dbo].[RSS_Project] ADD [VariationOptionsJson] VARCHAR(MAX) NULL;

--chnageset JCC:280425
UPDATE [dbo].[RSS_ServiceFuelType] SET [Title] = 'Solar/Electric' WHERE [ServiceFuelTypeCode] = 'SolarElectricity';


--changeset JCC:290425
ALTER TABLE [dbo].[RSS_OpeningTemplate] ADD [IsFavourite] BIT NOT NULL CONSTRAINT [DF_RSS_OpeningTemplate_IsFavourite] DEFAULT(0);
ALTER TABLE [dbo].[RSS_SurfaceTemplate] ADD [IsFavourite] BIT NOT NULL CONSTRAINT [DF_RSS_SurfaceTemplate_IsFavourite] DEFAULT(0);
ALTER TABLE [dbo].[RSS_ServiceTemplate] ADD [IsFavourite] BIT NOT NULL CONSTRAINT [DF_RSS_ServiceTemplate_IsFavourite] DEFAULT(0);

-- RSS_ServiceTemplateView will be updated at the end of the script

    -- RSS_SurfaceTemplateOpeningTemplateView will be updated at the end of the script


--changset JCC:020525
UPDATE [dbo].[RSS_Certification] SET [Title] = 'NCC 2022 Building Code of Australia - Volume Two Amendment 1' WHERE [CertificationId] = '476bafbf-8a52-4688-89d7-d4ce2ff5670f';

INSERT INTO [dbo].[AspNetRoles]
([Id],                                   [Name],                                                             [Description],                 [SortOrder], [ParentRoleId],                         [Type])
VALUES
('D5A5327B-C87B-4ED2-AA85-B065A05D3E7E', 'assessment_page_(tabs/sub-tabs)__specificationsummary__view',      'Specification Summary',       0260,        '05CFA092-4044-47A6-A130-950A8B17D8A2', 'Checkbox'),
('F2311FC4-CEDE-44B3-BA54-5F9972AF6D93', 'assessment_page_(tabs/sub-tabs)__specificationsummary__edit',      'Specification Summary',       0261,        '810A6306-7F2E-4449-A046-C8294FADF2FC', 'Checkbox'),
('67C5FAA6-F445-4CC0-B5BC-EC5C7A379B87', 'assessment_page_(tabs/sub-tabs)__compliancereport__view',          'Compliance Report',           0262,        '05CFA092-4044-47A6-A130-950A8B17D8A2', 'Checkbox'),
('921EE771-FA4F-40B5-AE14-F298CB9A6B84', 'assessment_page_(tabs/sub-tabs)__compliancereport__edit',          'Compliance Report',           0263,        '67C5FAA6-F445-4CC0-B5BC-EC5C7A379B87', 'Checkbox'),
('CEBDDC3B-21B7-4B97-B564-6BF7B819010C', 'assessment_page_(tabs/sub-tabs)__manuallyuploadeddocuments__view', 'Manually Uploaded Documents', 0264,        '05CFA092-4044-47A6-A130-950A8B17D8A2', 'Checkbox'),
('FAE18102-8989-4F92-91B6-51C2B3BD9D09', 'assessment_page_(tabs/sub-tabs)__manuallyuploadeddocuments__edit', 'Manually Uploaded Documents', 0265,        'CEBDDC3B-21B7-4B97-B564-6BF7B819010C', 'Checkbox'),
('32C92D3B-C380-4D2D-BF6C-0F863648B882', 'assessment_actions__regenerate',                                   'Regenerate',                  0318,        NULL,                                   'Checkbox');



--changeset JCC:100625

    -- Need to first increment SortOrder of all records above this new record so we can have a space to insert
WITH RecordsToUpdate AS ( SELECT Id, SortOrder FROM [dbo].[AspNetRoles] WHERE SortOrder BETWEEN 203 AND 299 )
UPDATE r SET SortOrder = r.SortOrder + 4 FROM [dbo].[AspNetRoles] r JOIN RecordsToUpdate ru ON r.Id = ru.Id;

    -- Insert
INSERT INTO [dbo].[AspNetRoles] VALUES ('62D8EA7A-1F94-40B9-B411-61C0C1745925', 'assessment_page_(tabs/sub-tabs)__client__view',                   NULL, 'Client',                         0203, 'D756A44A-79F8-44C7-836E-4D3AC270AD92', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('C4A8D4B9-99FE-4603-B3B2-ADDC963C380B', 'assessment_page_(tabs/sub-tabs)__client__edit',                   NULL, 'Client',                         0204, '62D8EA7A-1F94-40B9-B411-61C0C1745925', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('1BC28B29-45A6-4631-B5E4-FE32E3E87ADB', 'assessment_page_(tabs/sub-tabs)__customclientnamecheckbox__view', NULL, 'Custom Client Name (Check Box)', 0205, 'D756A44A-79F8-44C7-836E-4D3AC270AD92', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('6385D42A-E8C7-491B-9AEB-FEBA9A433A87', 'assessment_page_(tabs/sub-tabs)__customclientnamecheckbox__edit', NULL, 'Custom Client Name (Check Box)', 0206, '1BC28B29-45A6-4631-B5E4-FE32E3E87ADB', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('4A8A5263-EBDF-41C6-BF85-7588B5EAEDF7', 'assessment_page_(tabs/sub-tabs)__customclientname__view',         NULL, 'Custom Client Name',             0207, 'D756A44A-79F8-44C7-836E-4D3AC270AD92', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('C6B2518E-9EC5-4E8B-80F5-EDEAD006E3E1', 'assessment_page_(tabs/sub-tabs)__customclientname__edit',         NULL, 'Custom Client Name',             0208, '4A8A5263-EBDF-41C6-BF85-7588B5EAEDF7', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('61E2E914-6BF7-489A-A2C5-A7BC5D5F9CE1', 'assessment_page_(tabs/sub-tabs)__clientjobnumber__view',          NULL, 'Client Job Number',              0209, 'D756A44A-79F8-44C7-836E-4D3AC270AD92', 'Checkbox');
INSERT INTO [dbo].[AspNetRoles] VALUES ('DE6929EF-9A96-4700-BCF0-6B09C137697C', 'assessment_page_(tabs/sub-tabs)__clientjobnumber__edit',          NULL, 'Client Job Number',              0210, '61E2E914-6BF7-489A-A2C5-A7BC5D5F9CE1', 'Checkbox');

    -- Make [ClientJobNumber] nullable
ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] ALTER COLUMN [ClientJobNumber] VARCHAR(100) NULL;



-- changeset JCC:120625

-- Add index for JobModifiedOn to improve Show More performance
CREATE NONCLUSTERED INDEX IX_RSS_Job_ModifiedOn ON dbo.RSS_Job (
    ModifiedOn DESC,
    Deleted ASC,
	StatusCode ASC
) WITH( STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY];



--changeset JCC:130625
INSERT INTO [dbo].[AspNetRoles]
([Id],                                   [Name],                 [Discriminator], [Description], [SortOrder], [ParentRoleId], [Type])
VALUES
('E896F0C5-C21D-4E34-A86D-3F98540F10A8', 'energyLab__project', NULL,            'Project',     0280,        NULL,           'List__EnergyLabProject'),
('DCB8C8E4-C571-49DD-85C2-A42E2708BEBF', 'energyLab__client',  NULL,            'Client',      0281,        NULL,           'List__EnergyLabClient'),
('09569F7F-C91F-4377-87AE-C68D78B05084', 'energyLab__status',  NULL,            'Status',      0282,        NULL,           'List__EnergyLabStatus'),
('4B5CAB2C-815C-40BF-A7D4-370A580FDAEE', 'energyLab__type',    NULL,            'Type',        0283,        NULL,           'List__EnergyLabType'),
('E85161D5-2CC9-443F-AA8B-BC0F285D4565', 'energyLab__state',   NULL,            'State',       0284,        NULL,           'List__EnergyLabState');


--changeset JCC:230625
    -- [THR-752] New Edit/Reinstate Assessment Permissions

    -- "Edit Completed Assessment" (to be after "Edit Assessment")
UPDATE [dbo].[AspNetRoles] SET [SortOrder] = [SortOrder] + 1 WHERE [SortOrder] BETWEEN 304 AND 398;
INSERT INTO [dbo].[AspNetRoles]
([Id],                                   [Name],                                        [Discriminator], [Description],               [SortOrder], [ParentRoleId], [Type])
VALUES
('8DA9B054-316E-488C-A31B-D2C4652BCDFF', 'assessment_actions__editcompletedassessment', NULL,            'Edit Completed Assessment', 304,         NULL,           'Checkbox');

    -- "Reinstate Assessment" (to be after "Cancel Assessment")
UPDATE [dbo].[AspNetRoles] SET [SortOrder] = [SortOrder] + 1 WHERE [SortOrder] BETWEEN 306 AND 398;
INSERT INTO [dbo].[AspNetRoles]
([Id],                                   [Name],                                        [Discriminator], [Description],               [SortOrder], [ParentRoleId], [Type])
VALUES
('7115185B-D6DD-4327-93B7-89ABC6A95638', 'assessment_actions__reinstateassessment',     NULL,            'Reinstate Assessment',      306,         NULL,           'Checkbox');

--changeset JCC:300625
ALTER TABLE dbo.RSS_AssessmentComplianceOption ADD IsShownToClient bit NOT NULL CONSTRAINT DF_RSS_AssessmentComplianceOption_IsShownToClient DEFAULT(1);

--changeset JCC:010725
UPDATE [dbo].[AspNetRoles] SET SortOrder = SortOrder + 4 WHERE SortOrder BETWEEN 260 AND 279;
INSERT INTO [dbo].[AspNetRoles]
([Id],                                   [Name],                                                [Description],    [SortOrder], [ParentRoleId],                         [Type])
VALUES
('21126F0D-3B5C-4C10-81B3-AE80599BAF11', 'assessment_page_(tabs/sub-tabs)__valid__view',        'Valid',          260,        NULL,                                   'Checkbox'),
('734DE23C-5142-4C21-B693-25D65E6CF2AE', 'assessment_page_(tabs/sub-tabs)__valid__edit',        'Valid',          261,        '21126F0D-3B5C-4C10-81B3-AE80599BAF11', 'Checkbox'),
('59E114F9-B218-4BB3-8EB6-2D203E4BC9B8', 'assessment_page_(tabs/sub-tabs)__showtoclient__view', 'Show to Client', 262,        NULL,                                   'Checkbox'),
('530F4DC3-C1D3-4286-8D00-DD9D293AE9F6', 'assessment_page_(tabs/sub-tabs)__showtoclient__edit', 'Show to Client', 263,        '59E114F9-B218-4BB3-8EB6-2D203E4BC9B8', 'Checkbox');



--changeset JCC:010725_2
ALTER TABLE [dbo].[RSS_ComplianceMethod] ADD PreparedByOverrideUserId UNIQUEIDENTIFIER NULL CONSTRAINT [FK_RSS_ComplianceMethod_AspNetUserId] REFERENCES [dbo].[RSS_User] ([UserId]);



--changeset JCC:020725
CREATE INDEX [IX_RSS_StandardHomeModel_Deleted_ProjectId]                ON [dbo].[RSS_StandardHomeModel]       (Deleted, ProjectId, IsVariationOfHomeModelId, StandardHomeModelId);
CREATE INDEX [IX_RSS_StandardHomeModel_Deleted_IsVariationOfHomeModelId] ON [dbo].[RSS_StandardHomeModel]       (Deleted, IsVariationOfHomeModelId, StandardHomeModelId);
CREATE INDEX [IX_RSS_StandardHomeModel_IsActive]                         ON [dbo].[RSS_StandardHomeModel]       (IsActive, StandardHomeModelId);
CREATE INDEX [IX_RSS_StandardHomeModelOption_StandardHomeModelId]        ON [dbo].[RSS_StandardHomeModelOption] (StandardHomeModelId, AssessmentMethod, NatHERSClimateZone , NorthOffset, RoofConstruction, CeilingInsulation, InteriorWallInsulation);
CREATE INDEX [IX_RSS_StandardHomeModelFile_Deleted]                      ON [dbo].[RSS_StandardHomeModelFile]   (Deleted, StandardHomeModelId);

-- Give "<EMAIL>" all Roles
INSERT INTO [dbo].[AspNetUserRoles]
([UserId], [RoleId])
SELECT 'C65BA6BD-2D80-4BEE-B213-6ACFA0016C15', [role].[Id]
FROM [dbo].[AspNetRoles] [role]
WHERE NOT EXISTS (
    SELECT 1 
    FROM [dbo].[AspNetUserRoles] [userRole]
    WHERE [userRole].[UserId] = 'C65BA6BD-2D80-4BEE-B213-6ACFA0016C15'
      AND [userRole].[RoleId] = [role].[Id]
);

    PRINT 'DML operations completed successfully.'
    COMMIT TRANSACTION ScriptsForLive_DML

    PRINT 'Scripts For Live database migration completed successfully!'
    PRINT 'All database migration operations have been applied.'

END TRY
BEGIN CATCH
    -- An error occurred, rollback the transaction
    PRINT 'Error occurred during DML operations: ' + ERROR_MESSAGE()
    ROLLBACK TRANSACTION ScriptsForLive_DML

    -- Re-raise the error using RAISERROR for compatibility
    DECLARE @DMLErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
    DECLARE @DMLErrorSeverity INT = ERROR_SEVERITY()
    DECLARE @DMLErrorState INT = ERROR_STATE()
    RAISERROR(@DMLErrorMessage, @DMLErrorSeverity, @DMLErrorState)
END CATCH

-- Allow OPENJSON sql function
-- Note: Database compatibility level should be set manually for the target database
-- ALTER DATABASE [YourDatabaseName] SET COMPATIBILITY_LEVEL = 130;

-- =============================================
-- CREATE VIEW STATEMENTS
-- These are moved outside the transaction to avoid batch separation issues
-- =============================================

GO

-- Create vw_AspNetUserRoles
CREATE VIEW [dbo].[vw_AspNetUserRoles]
AS
SELECT [UserId],
       [RoleId]
FROM [dbo].[AspNetUserRoles];

GO

-- Create RSS_SurfaceTemplateOpeningTemplateView
IF OBJECT_ID('dbo.RSS_SurfaceTemplateOpeningTemplateView', 'V') IS NOT NULL
    DROP VIEW dbo.RSS_SurfaceTemplateOpeningTemplateView
GO
CREATE VIEW dbo.RSS_SurfaceTemplateOpeningTemplateView
AS
SELECT
    'surface' AS [Type],
    ConstructionId,
    surface.[Description],
    DisplayDescription,
    cc.Title as ConstructionCategoryTitle,
    cc.ConstructionCategoryCode,
    csc.Title as ConstructionSubCategoryTitle,
    csc.ConstructionSubCategoryCode,
    os.Title as OpeningStyleTitle,
    os.OpeningStyleCode,
    manufacturer.[Description] AS ManufacturerDescription,
    surface.ManufacturerId,
    Comments,
    surface.[Deleted],
    surface.[CreatedOn],
    'DO_NOT_PROCESS' as PerformanceJson, -- sigh
    surface.[IsFavourite],
    surface.ExternalConstructionId,
    -- Add insulation data fields
    CASE
        WHEN surface.InsulationDataJson IS NOT NULL AND surface.InsulationDataJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(surface.InsulationDataJson, '$.HasData') = 'true'
                THEN JSON_VALUE(surface.InsulationDataJson, '$.Description')
                ELSE ''
            END
        ELSE ''
    END as InsulationDescription,
    -- Glass fields are not applicable for surfaces, set to NULL
    NULL as GlassTypeTitle,
    NULL as GlassColourTitle,
    NULL as LowECoating,
    -- Frame material field is not applicable for surfaces, set to NULL
    NULL as FrameMaterialTitle,
    -- Performance fields are not applicable for surfaces, set to NULL
    NULL as UValue,
    NULL as SHGC
FROM
    dbo.RSS_SurfaceTemplate as surface
        JOIN dbo.RSS_ConstructionCategory cc ON cc.ConstructionCategoryCode = surface.ConstructionCategoryCode
        LEFT JOIN dbo.RSS_ConstructionSubCategory csc ON csc.ConstructionSubCategoryCode = surface.ConstructionSubCategoryCode
        LEFT JOIN dbo.RSS_OpeningStyle os ON os.OpeningStyleCode = surface.OpeningStyleCode
        LEFT JOIN dbo.RSS_Manufacturer manufacturer ON manufacturer.ManufacturerId = surface.ManufacturerId
UNION ALL
SELECT
    'opening' AS [Type],
    ConstructionId,
    opening.[Description],
    DisplayDescription,
    cc.Title as ConstructionCategoryTitle,
    cc.ConstructionCategoryCode,
    csc.Title as ConstructionSubCategoryTitle,
    csc.ConstructionSubCategoryCode,
    os.Title as OpeningStyleTitle,
    os.OpeningStyleCode,
    manufacturer.[Description] AS ManufacturerDescription,
    opening.ManufacturerId,
    Comments,
    opening.[Deleted],
    opening.[CreatedOn],
    opening.PerformanceJson,
    opening.[IsFavourite],
    opening.ExternalConstructionId,
    -- Openings don't have insulation data, so set defaults
    NULL as InsulationDescription,
    -- Extract Glass Type from GlassDataJson
    CASE
        WHEN opening.GlassDataJson IS NOT NULL AND opening.GlassDataJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(opening.GlassDataJson, '$.Type.Title') IS NOT NULL
                THEN JSON_VALUE(opening.GlassDataJson, '$.Type.Title')
                ELSE ''
            END
        ELSE ''
    END as GlassTypeTitle,
    -- Extract Glass Colour from GlassDataJson
    CASE
        WHEN opening.GlassDataJson IS NOT NULL AND opening.GlassDataJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(opening.GlassDataJson, '$.Colour.Title') IS NOT NULL
                THEN JSON_VALUE(opening.GlassDataJson, '$.Colour.Title')
                ELSE ''
            END
        ELSE ''
    END as GlassColourTitle,
    -- Extract Low-E Coating from GlassDataJson
    CASE
        WHEN opening.GlassDataJson IS NOT NULL AND opening.GlassDataJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(opening.GlassDataJson, '$.HasLowECoating') = 'true'
                THEN 'Yes'
                WHEN JSON_VALUE(opening.GlassDataJson, '$.HasLowECoating') = 'false'
                THEN 'No'
                ELSE ''
            END
        ELSE ''
    END as LowECoating,
    -- Add frame material field for openings
    ISNULL(fm.Title, '') as FrameMaterialTitle,
    -- Extract U-Value from PerformanceJson for openings
    CASE
        WHEN opening.PerformanceJson IS NOT NULL AND opening.PerformanceJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(opening.PerformanceJson, '$.UValue') IS NOT NULL
                THEN CAST(JSON_VALUE(opening.PerformanceJson, '$.UValue') AS DECIMAL(15,5))
                ELSE NULL
            END
        ELSE NULL
    END as UValue,
    -- Extract SHGC from PerformanceJson for openings
    CASE
        WHEN opening.PerformanceJson IS NOT NULL AND opening.PerformanceJson != ''
        THEN
            CASE
                WHEN JSON_VALUE(opening.PerformanceJson, '$.SHGC') IS NOT NULL
                THEN CAST(JSON_VALUE(opening.PerformanceJson, '$.SHGC') AS DECIMAL(15,5))
                ELSE NULL
            END
        ELSE NULL
    END as SHGC
FROM
    dbo.RSS_OpeningTemplate as opening
        JOIN dbo.RSS_ConstructionCategory cc ON cc.ConstructionCategoryCode = opening.ConstructionCategoryCode
        LEFT JOIN dbo.RSS_ConstructionSubCategory csc ON csc.ConstructionSubCategoryCode = opening.ConstructionSubCategoryCode
        LEFT JOIN dbo.RSS_OpeningStyle os ON os.OpeningStyleCode = opening.OpeningStyleCode
        LEFT JOIN dbo.RSS_Manufacturer manufacturer ON manufacturer.ManufacturerId = opening.ManufacturerId
        LEFT JOIN dbo.RSS_FrameMaterial fm ON fm.FrameMaterialCode = opening.FrameMaterialCode
GO

-- Create RSS_ServiceTemplateView
IF OBJECT_ID('dbo.RSS_ServiceTemplateView', 'V') IS NOT NULL
    DROP VIEW dbo.RSS_ServiceTemplateView
GO
CREATE VIEW dbo.RSS_ServiceTemplateView
AS
SELECT  template.ServiceTemplateId,
        template.ServiceCategoryCode,
        category.Title as ServiceCategoryTitle,
        template.Description,

        manufacturer.[Description] AS ManufacturerDescription,
        template.ManufacturerId,

        template.Comments,
        template.Deleted,
        template.CreatedOn,
        template.IsFavourite

FROM dbo.RSS_ServiceTemplate template
         JOIN dbo.RSS_ServiceCategory category ON category.ServiceCategoryCode = template.ServiceCategoryCode
         LEFT JOIN dbo.RSS_Manufacturer manufacturer ON manufacturer.ManufacturerId = template.ManufacturerId;

GO

-- Create RSS_JobAssessmentProjectDetail_View
IF OBJECT_ID('dbo.RSS_JobAssessmentProjectDetail_View', 'V') IS NOT NULL
    DROP VIEW [dbo].[RSS_JobAssessmentProjectDetail_View]
GO
CREATE VIEW [dbo].[RSS_JobAssessmentProjectDetail_View]
AS
SELECT [job].[JobId]                                                                                                  [JobId],
       [job].[CurrentAssessmentId]                                                                                    [CurrentAssessmentId],
       [job].[JobReference]                                                                                           [JobReference],
       [job].[ClientId]                                                                                               [ClientId],
       [client].[ClientName]                                                                                          [ClientName],
       [job].[StatusCode]                                                                                             [StatusCode],
       [status].[Description]                                                                                         [JobStatusDescription],
       [job].[CreatedOn]                                                                                              [JobCreatedOn],
       [job].[ModifiedOn]                                                                                             [JobModifiedOn],
       [job].[Deleted]                                                                                                [JobDeleted],
       [assessor].[UserId]                                                                                            [AssessorUserId],
       [assessor].[FullName]                                                                                          [AssessorFullName],
       [assessment].[PriorityCode]                                                                                    [AssessmentPriorityCode],
       [building].[Design]                                                                                            [AssessmentDesign],
       [assessment].[StatusCode]                                                                                      [AssessmentStatusCode],
       [assessment].[CreatedOn]                                                                                       [AssessmentCreatedOn],
       [assessment].[CerficateDate]                                                                                   [AssessmentCerficateDate],
       [assessment].[Deleted]                                                                                         [AssessmentDeleted],
       [detail].[ClientJobNumber]                                                                                     [ClientJobNumber],
       [detail].[ProjectOwner]                                                                                        [ProjectOwner],
       [detail].[OrderDate]                                                                                           [OrderDate],
       CASE WHEN [detail].[UseCustomAddress] = 1
            THEN [detail].[CustomDisplayAddress]
            ELSE [detail].[FullAddress] END                                                                           [Address],
       [orderType].[Description]                                                                                      [OrderTypeDescription],
       [creator].[FullName]                                                                                           [CreatorFullName],
       [clientAssignee].[FullName]                                                                                    [ClientAssigneeFullName],
       [building].[ProjectDescriptionCode]                                                                            [ProjectDescriptionCode],
       [Description].[Description]                                                                                    [ProjectDescriptionDescription],
       [complianceMethod].[ComplianceMethodCode]                                                                      [ComplianceMethodCode],
       [complianceMethod].[Description]                                                                               [ComplianceMethodDescription],
       [detail].[AssessmentVersion]                                                                                   [AssessmentVersion],
       [detail].[CreatedOn]                                                                                           [AssessmentProjectDetailCreatedOn],
       [detail].[Deleted]                                                                                             [AssessmentProjectDetailDeleted],
       [priority].[Description]                                                                                       [AssessmentPriorityDescription],
       RIGHT([assessment].[NatHERSClimateZoneCode], LEN([assessment].[NatHERSClimateZoneCode])-3)                     [AssessmentNatHERSClimateZone],
       RIGHT([assessment].[NCCClimateZoneCode], LEN([assessment].[NCCClimateZoneCode])-3)                             [AssessmentNCCClimateZone],
       [building].[BuildingOrientation]                                                                               [NorthOffset],
       [building].[Heating]                                                                                           [Heating],
       [building].[Cooling]                                                                                           [Cooling],
       [building].[TotalEnergyLoad]                                                                                   [TotalEnergyLoad],
       CASE WHEN [building].[HouseEnergyRatingOverride] IS NOT NULL
            THEN [building].[HouseEnergyRatingOverride]
            ELSE [building].[HouseEnergyRating] END                                                                   [CalculatedHouseEnergyRating],
       [detail].[LotDescription]                                                                                      [AssessmentProjectDetailLotDescription],
       [detail].[LotWidth]                                                                                            [AssessmentProjectDetailLotWidth],
       [detail].[LotLength]                                                                                           [AssessmentProjectDetailLotLength],
       [detail].[ParcelArea]                                                                                          [AssessmentProjectDetailParcelArea],
       [detail].[CornerBlock]                                                                                         [AssessmentProjectDetailCornerBlock],
       [detail].[RearLaneway]                                                                                         [AssessmentProjectDetailRearLaneway],
       [detail].[RuralLot]                                                                                            [AssessmentProjectDetailRuralLot],
       [certification].[CertificationId]                                                                              [CertificationId],
       [certification].[Title]                                                                                        [CertificationTitle],
       [garageResult].[Location]                                                                                      [GarageLocation],
       [outdoorLivingResult].[Location]                                                                               [OutdoorLivingLocation],
       [detail].[ComplianceCost]                                                                                      [AssessmentProjectDetailComplianceCost],
       [detail].[Suburb]                                                                                              [AssessmentProjectDetailSuburb],
       [detail].[LocalGovernmentAuthority]                                                                            [AssessmentProjectDetailLGA],
       [detail].[LocalGovernmentAuthorityShort]                                                                       [AssessmentProjectDetailLGAShort]
FROM [dbo].[RSS_Job] [job]
INNER JOIN      [dbo].[RSS_Assessment] [assessment]                 ON [job].[CurrentAssessmentId] = [assessment].[AssessmentId]
INNER JOIN      [dbo].[RSS_AssessmentComplianceOption] [option]     ON [option].[AssessmentId] = [assessment].[AssessmentId] AND [option].[IsBaselineSimulation] = 1
INNER JOIN      [dbo].[RSS_AssessmentComplianceBuilding] [building] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId] AND [building].[AssessmentComplianceBuildingId] = [option].[ProposedBuildingId]
INNER JOIN      [dbo].[RSS_AssessmentProjectDetail] [detail]        ON [assessment].[AssessmentId] = [detail].[AssessmentId]
INNER JOIN      [dbo].[RSS_Client] [client]                         ON [job].[ClientId] = [client].[ClientId]
INNER JOIN      [dbo].[RSS_ComplianceMethod] [complianceMethod]     ON [option].[ComplianceMethodCode] = [complianceMethod].[ComplianceMethodCode]
LEFT JOIN       [dbo].[RSS_Certification] [certification]           ON [option].[CertificationId] = [certification].[CertificationId]
LEFT JOIN       [dbo].[RSS_OrderType] [orderType]                   ON [detail].[OrderTypeCode] = [orderType].[OrderTypeCode]
INNER JOIN      [dbo].[RSS_Status] [status]                         ON [job].[StatusCode] = [status].[StatusCode]
LEFT OUTER JOIN [dbo].[RSS_Priority] [priority]                     ON [assessment].[PriorityCode] = [priority].[PriorityCode]
LEFT OUTER JOIN [dbo].[RSS_User] [creator]                          ON [detail].[CreatorUserId] = [creator].[UserId]
LEFT OUTER JOIN [dbo].[RSS_User] [clientAssignee]                   ON [detail].[ClientAssigneeUserId] = [ClientAssignee].[UserId]
LEFT JOIN       [dbo].[RSS_User] [assessor]                         ON [assessment].[AssessorUserId] = [Assessor].[UserId]
LEFT OUTER JOIN [dbo].[RSS_ProjectDescription] [description]        ON [building].[ProjectDescriptionCode] = [description].[ProjectDescriptionCode]
OUTER APPLY (
    SELECT TOP (1) JSON_VALUE([GarageSpacesJson].[Value], '$.Location') AS [Location]
    FROM (
        SELECT *
        FROM OPENJSON((
            SELECT TOP(1) [SpacesJson]
            FROM [dbo].[RSS_AssessmentComplianceBuilding] [building]
            INNER JOIN [dbo].[RSS_AssessmentComplianceOption] [option] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId]
            WHERE [building].[Deleted] = 0
                AND [option].[Deleted] = 0
                AND [option].[AssessmentId] = [job].[CurrentAssessmentId]
                AND [option].[OptionIndex] = 0
                AND [building].[SpacesJson] IS NOT NULL AND [building].[SpacesJson] <> '[]'
        ), '$') AS [ZoneTypeCode]
    ) [GarageSpacesJson]
    WHERE JSON_VALUE([GarageSpacesJson].[Value], '$.Deleted') = 'false'
        AND JSON_VALUE([GarageSpacesJson].[Value], '$.ZoneType.Deleted') = 'false'
        AND JSON_VALUE([GarageSpacesJson].[Value], '$.ZoneType.ZoneTypeCode') = 'ZTGarage'
) [garageResult]
OUTER APPLY (
    SELECT TOP(1) JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.Location') AS [Location]
    FROM (
        SELECT *
        FROM OPENJSON((
            SELECT TOP(1) [SpacesJson]
            FROM [dbo].[RSS_AssessmentComplianceBuilding] [building]
            INNER JOIN [dbo].[RSS_AssessmentComplianceOption] [option] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId]
            WHERE [building].[Deleted] = 0
                AND [option].[Deleted] = 0
                AND [option].[AssessmentId] = [job].[CurrentAssessmentId]
                AND [option].[OptionIndex] = 0
                AND [building].[SpacesJson] IS NOT NULL AND [building].[SpacesJson] <> '[]'
        ), '$') AS [ZoneTypeCode]
    ) [OutdoorLivingSpacesJson]
    WHERE JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.Deleted') = 'false'
        AND JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.ZoneType.Deleted') = 'false'
        AND JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.ZoneType.ZoneTypeCode') = 'ZTOutdoor'
) [outdoorLivingResult];

GO

-- Create RSS_JobAnalyticsDetail_View
CREATE VIEW [dbo].[RSS_JobAnalyticsDetail_View]
AS
SELECT [job].[JobId]                                                [JobId],
       [job].[CurrentAssessmentId]                                  [CurrentAssessmentId],
       [job].[JobReference]                                         [JobReference],
       [job].[ClientId]                                             [ClientId],
       [detail].[ClientJobNumber]                                   [ClientJobNumber],
       [detail].[ProjectOwner]                                      [ProjectOwner],
       CASE WHEN [detail].[UseCustomAddress] = 1
            THEN [detail].[CustomDisplayAddress]
            ELSE [detail].[FullAddress] END                         [Address],
       [creator].[FullName]                                         [CreatorFullName],
       [job].[StatusCode]                                           [StatusCode],
       [job].[CreatedOn]                                            [JobCreatedOn],
       [assessment].[CerficateDate]                                 [AssessmentCerficateDate],
       [job].[ModifiedOn]                                           [JobModifiedOn],
       [job].[Deleted]                                              [JobDeleted],
       [assessment].[Deleted]                                       [AssessmentDeleted],
       [client].[ClientName]                                        [ClientName],
       [building].[ProjectDescriptionCode]                          [ProjectDescriptionCode],
       [Description].[Description]                                  [ProjectDescriptionDescription],
       [building].[Design]                                          [AssessmentDesign],
       [assessment].[StatusCode]                                    [AssessmentStatusCode],
       [priority].[Description]                                     [AssessmentPriorityDescription],
       [detail].[Suburb]                                            [AssessmentProjectDetailSuburb],
       [detail].[LocalGovernmentAuthority]                          [AssessmentProjectDetailLGA],
       [status].[Description]                                       [JobStatusDescription],
       [clientAssignee].[FullName]                                  [ClientAssigneeFullName],
       [assessor].[UserId]                                          [AssessorUserId],
       [assessor].[FullName]                                        [AssessorFullName],
       [detail].[AssessmentVersion]                                 [AssessmentVersion],
       [detail].[LotDescription]                                    [AssessmentProjectDetailLotDescription],
       [detail].[LotWidth]                                          [AssessmentProjectDetailLotWidth],
       [detail].[LotLength]                                         [AssessmentProjectDetailLotLength],
       [detail].[ParcelArea]                                        [AssessmentProjectDetailParcelArea],
       [detail].[CornerBlock]                                       [AssessmentProjectDetailCornerBlock],
       [detail].[RearLaneway]                                       [AssessmentProjectDetailRearLaneway],
       [detail].[RuralLot]                                          [AssessmentProjectDetailRuralLot],
       CONVERT(INT, [building].[BuildingOrientation])               [NorthOffset],
       [certification].[CertificationId]                            [CertificationId],
       [certification].[Title]                                      [CertificationTitle],
       [garageResult].[Location]                                    [GarageLocation],
       [outdoorLivingResult].[Location]                             [OutdoorLivingLocation],
       [complianceMethod].[ComplianceMethodCode]                    [ComplianceMethodCode],
       [complianceMethod].[Description]                             [ComplianceMethodDescription],
       [narHers].[Description]                                      [NatHersClimateZone],
       RIGHT(
        [assessment].[NCCClimateZoneCode],
        LEN([assessment].[NCCClimateZoneCode])-3
       )                                                            [NCCClimateZone],
       [building].[Heating]                                         [Heating],
       [building].[Cooling]                                         [Cooling],
       [building].[TotalEnergyLoad]                                 [TotalEnergyLoad],
       CASE WHEN [building].[HouseEnergyRatingOverride] IS NOT NULL
            THEN [building].[HouseEnergyRatingOverride]
            ELSE [building].[HouseEnergyRating] END                 [CalculatedHouseEnergyRating],
       ISNULL(CONVERT(BIT, 1), CONVERT(BIT, 1))                     [IsActive], -- Make sure edmx picks this up as not null
       ISNULL(CONVERT(BIT, 0), CONVERT(BIT, 0))                     [IsVirtualSim] -- Make sure edmx picks this up as not null
FROM [dbo].[RSS_Job] [job]
INNER JOIN      [dbo].[RSS_Assessment] [assessment]                 ON [job].[CurrentAssessmentId] = [assessment].[AssessmentId]
INNER JOIN      [dbo].[RSS_AssessmentComplianceOption] [option]     ON [option].[AssessmentId] = [assessment].[AssessmentId] AND [option].[IsBaselineSimulation] = 1
INNER JOIN      [dbo].[RSS_ComplianceMethod] [complianceMethod]     ON [option].[ComplianceMethodCode] = [complianceMethod].[ComplianceMethodCode]
LEFT JOIN       [dbo].[RSS_Certification] [certification]           ON [option].[CertificationId] = [certification].[CertificationId]
INNER JOIN      [dbo].[RSS_AssessmentComplianceBuilding] [building] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId] AND [building].[AssessmentComplianceBuildingId] = [option].[ProposedBuildingId]
INNER JOIN      [dbo].[RSS_AssessmentProjectDetail] [detail]        ON [assessment].[AssessmentId] = [detail].[AssessmentId]
INNER JOIN      [dbo].[RSS_Client] [client]                         ON [job].[ClientId] = [client].[ClientId]
INNER JOIN      [dbo].[RSS_Status] [status]                         ON [job].[StatusCode] = [status].[StatusCode]
LEFT OUTER JOIN [dbo].[RSS_Priority] [priority]                     ON [assessment].[PriorityCode] = [priority].[PriorityCode]
LEFT OUTER JOIN [dbo].[RSS_User] [creator]                          ON [detail].[CreatorUserId] = [creator].[UserId]
LEFT OUTER JOIN [dbo].[RSS_User] [clientAssignee]                   ON [detail].[ClientAssigneeUserId] = [ClientAssignee].[UserId]
LEFT JOIN       [dbo].[RSS_User] [assessor]                         ON [assessment].[AssessorUserId] = [Assessor].[UserId]
LEFT OUTER JOIN [dbo].[RSS_ProjectDescription] [description]        ON [building].[ProjectDescriptionCode] = [description].[ProjectDescriptionCode]
LEFT JOIN       [dbo].[RSS_NatHERSClimateZone] [narHers]            ON [assessment].[NatHERSClimateZoneCode] = [narHers].[NatHERSClimateZoneCode]
OUTER APPLY (
    SELECT TOP (1) JSON_VALUE([GarageSpacesJson].[Value], '$.Location') AS [Location]
    FROM (
        SELECT *
        FROM OPENJSON((
            SELECT TOP(1) [SpacesJson]
            FROM [dbo].[RSS_AssessmentComplianceBuilding] [building]
            INNER JOIN [dbo].[RSS_AssessmentComplianceOption] [option] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId]
            WHERE [building].[Deleted] = 0
                AND [option].[Deleted] = 0
                AND [option].[AssessmentId] = [job].[CurrentAssessmentId]
                AND [option].[OptionIndex] = 0
                AND [building].[SpacesJson] IS NOT NULL AND [building].[SpacesJson] <> '[]'
        ), '$') AS [ZoneTypeCode]
    ) [GarageSpacesJson]
    WHERE JSON_VALUE([GarageSpacesJson].[Value], '$.Deleted') = 'false'
        AND JSON_VALUE([GarageSpacesJson].[Value], '$.ZoneType.Deleted') = 'false'
        AND JSON_VALUE([GarageSpacesJson].[Value], '$.ZoneType.ZoneTypeCode') = 'ZTGarage'
) [garageResult]
OUTER APPLY (
    SELECT TOP(1) JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.Location') AS [Location]
    FROM (
        SELECT *
        FROM OPENJSON((
            SELECT TOP(1) [SpacesJson]
            FROM [dbo].[RSS_AssessmentComplianceBuilding] [building]
            INNER JOIN [dbo].[RSS_AssessmentComplianceOption] [option] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId]
            WHERE [building].[Deleted] = 0
                AND [option].[Deleted] = 0
                AND [option].[AssessmentId] = [job].[CurrentAssessmentId]
                AND [option].[OptionIndex] = 0
                AND [building].[SpacesJson] IS NOT NULL AND [building].[SpacesJson] <> '[]'
        ), '$') AS [ZoneTypeCode]
    ) [OutdoorLivingSpacesJson]
    WHERE JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.Deleted') = 'false'
        AND JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.ZoneType.Deleted') = 'false'
        AND JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.ZoneType.ZoneTypeCode') = 'ZTOutdoor'
) [outdoorLivingResult]
UNION
SELECT [job].[JobId]                                                                      [JobId],
       [job].[CurrentAssessmentId]                                                        [CurrentAssessmentId],
       [job].[JobReference]                                                               [JobReference],
       [job].[ClientId]                                                                   [ClientId],
       CONCAT(
            [detail].[ClientJobNumber],
            CASE WHEN [nccResult].[FlippedFromId] IS NULL
                THEN ' (vsim)'
                ELSE ' (vsim_flip)'
            END
       )                                                                                  [ClientJobNumber],
       [detail].[ProjectOwner]                                                            [ProjectOwner],
       CASE WHEN [detail].[UseCustomAddress] = 1
            THEN [detail].[CustomDisplayAddress]
            ELSE [detail].[FullAddress] END                                               [Address],
       [creator].[FullName]                                                               [CreatorFullName],
       [job].[StatusCode]                                                                 [StatusCode],
       [job].[CreatedOn]                                                                  [JobCreatedOn],
       [assessment].[CerficateDate]                                                       [AssessmentCerficateDate],
       [job].[ModifiedOn]                                                                 [JobModifiedOn],
       [job].[Deleted]                                                                    [JobDeleted],
       [assessment].[Deleted]                                                             [AssessmentDeleted],
       [client].[ClientName]                                                              [ClientName],
       [building].[ProjectDescriptionCode]                                                [ProjectDescriptionCode],
       [Description].[Description]                                                        [ProjectDescriptionDescription],
       [building].[Design]                                                                [AssessmentDesign],
       [assessment].[StatusCode]                                                          [AssessmentStatusCode],
       [priority].[Description]                                                           [AssessmentPriorityDescription],
       [detail].[Suburb]                                                                  [AssessmentProjectDetailSuburb],
       [detail].[LocalGovernmentAuthority]                                                [AssessmentProjectDetailLGA],
       [status].[Description]                                                             [JobStatusDescription],
       [clientAssignee].[FullName]                                                        [ClientAssigneeFullName],
       [assessor].[UserId]                                                                [AssessorUserId],
       [assessor].[FullName]                                                              [AssessorFullName],
       [detail].[AssessmentVersion]                                                       [AssessmentVersion],
       [detail].[LotDescription]                                                          [AssessmentProjectDetailLotDescription],
       [detail].[LotWidth]                                                                [AssessmentProjectDetailLotWidth],
       [detail].[LotLength]                                                               [AssessmentProjectDetailLotLength],
       [detail].[ParcelArea]                                                              [AssessmentProjectDetailParcelArea],
       CASE WHEN [nccResult].[FlippedFromId] IS NOT NULL THEN (
            CASE WHEN [detail].[CornerBlock] LIKE '%Left%'
                    THEN REPLACE([detail].[CornerBlock], 'Left', 'Right')
                 WHEN [detail].[CornerBlock] LIKE '%Right%'
                    THEN REPLACE([detail].[CornerBlock], 'Right', 'Left')
                 ELSE [detail].[CornerBlock] END
        ) ELSE [detail].[CornerBlock] END                                                 [AssessmentProjectDetailCornerBlock],
       [detail].[RearLaneway]                                                             [AssessmentProjectDetailRearLaneway],
       [detail].[RuralLot]                                                                [AssessmentProjectDetailRuralLot],
       CONVERT(INT, ([nccOffsetListResult].[key] * 45))                                   [NorthOffset],
       [certification].[CertificationId]                                                  [CertificationId],
       [nccResult].[Certification]                                                        [CertificationTitle],
       CASE WHEN [nccResult].[FlippedFromId] IS NOT NULL THEN (
            CASE WHEN [garageResult].[Location] LIKE '%Left%'
                    THEN REPLACE([garageResult].[Location], 'Left', 'Right')
                 WHEN [garageResult].[Location] LIKE '%Right%'
                    THEN REPLACE([garageResult].[Location], 'Right', 'Left')
                 ELSE [garageResult].[Location] END
        ) ELSE [garageResult].[Location] END                                              [GarageLocation],
       CASE WHEN [nccResult].[FlippedFromId] IS NOT NULL THEN (
            CASE WHEN [outdoorLivingResult].[Location] LIKE '%Left%'
                    THEN REPLACE([outdoorLivingResult].[Location], 'Left', 'Right')
                 WHEN [outdoorLivingResult].[Location] LIKE '%Right%'
                    THEN REPLACE([outdoorLivingResult].[Location], 'Right', 'Left')
                 ELSE [outdoorLivingResult].[Location] END
        ) ELSE [outdoorLivingResult].[Location] END                                       [OutdoorLivingLocation],
       [complianceMethod].[ComplianceMethodCode]                                          [ComplianceMethodCode],
       [complianceMethod].[Description]                                                   [ComplianceMethodDescription],
       [nccResult].[NatHersClimateZone]                                                   [NatHersClimateZone],
       RIGHT(
        [assessment].[NCCClimateZoneCode],
        LEN([assessment].[NCCClimateZoneCode])-3
       )                                                            [NCCClimateZone],
       JSON_VALUE([nccOffsetListResult].[value], '$.Heating')                             [Heating],
       JSON_VALUE([nccOffsetListResult].[value], '$.Cooling')                             [Cooling],
       JSON_VALUE([nccOffsetListResult].[value], '$.Total')                               [TotalEnergyLoad],
       JSON_VALUE([nccOffsetListResult].[value], '$.HER')                                 [CalculatedHouseEnergyRating],
       ISNULL(CONVERT(BIT, [nccResult].[IsActive]), CONVERT(BIT, 1))                      [IsActive], -- Make sure edmx picks this up as not null
       ISNULL(CONVERT(BIT, 1), CONVERT(BIT, 1))                                           [IsVirtualSim] -- Make sure edmx picks this up as not null
FROM [dbo].[RSS_Job] [job]
INNER JOIN      [dbo].[RSS_Assessment] [assessment]                 ON [job].[CurrentAssessmentId] = [assessment].[AssessmentId]
INNER JOIN      [dbo].[RSS_AssessmentComplianceOption] [option]     ON [option].[AssessmentId] = [assessment].[AssessmentId] AND [option].[IsBaselineSimulation] = 1
INNER JOIN      [dbo].[RSS_ComplianceMethod] [complianceMethod]     ON [option].[ComplianceMethodCode] = [complianceMethod].[ComplianceMethodCode]
INNER JOIN      [dbo].[RSS_AssessmentComplianceBuilding] [building] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId] AND [building].[AssessmentComplianceBuildingId] = [option].[ProposedBuildingId]
INNER JOIN      [dbo].[RSS_AssessmentProjectDetail] [detail]        ON [assessment].[AssessmentId] = [detail].[AssessmentId]
INNER JOIN      [dbo].[RSS_Client] [client]                         ON [job].[ClientId] = [client].[ClientId]
INNER JOIN      [dbo].[RSS_Status] [status]                         ON [job].[StatusCode] = [status].[StatusCode]
LEFT OUTER JOIN [dbo].[RSS_Priority] [priority]                     ON [assessment].[PriorityCode] = [priority].[PriorityCode]
LEFT OUTER JOIN [dbo].[RSS_User] [creator]                          ON [detail].[CreatorUserId] = [creator].[UserId]
LEFT OUTER JOIN [dbo].[RSS_User] [clientAssignee]                   ON [detail].[ClientAssigneeUserId] = [ClientAssignee].[UserId]
LEFT JOIN       [dbo].[RSS_User] [assessor]                         ON [assessment].[AssessorUserId] = [Assessor].[UserId]
LEFT OUTER JOIN [dbo].[RSS_ProjectDescription] [description]        ON [building].[ProjectDescriptionCode] = [description].[ProjectDescriptionCode]
LEFT JOIN       [dbo].[RSS_NatHERSClimateZone] [narHers]            ON [assessment].[NatHERSClimateZoneCode] = [narHers].[NatHERSClimateZoneCode]
OUTER APPLY (
    SELECT TOP (1) JSON_VALUE([GarageSpacesJson].[Value], '$.Location') AS [Location]
    FROM (
        SELECT *
        FROM OPENJSON((
            SELECT TOP(1) [SpacesJson]
            FROM [dbo].[RSS_AssessmentComplianceBuilding] [building]
            INNER JOIN [dbo].[RSS_AssessmentComplianceOption] [option] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId]
            WHERE [building].[Deleted] = 0
                AND [option].[Deleted] = 0
                AND [option].[AssessmentId] = [job].[CurrentAssessmentId]
                AND [option].[OptionIndex] = 0
                AND [building].[SpacesJson] IS NOT NULL AND [building].[SpacesJson] <> '[]'
        ), '$') AS [ZoneTypeCode]
    ) [GarageSpacesJson]
    WHERE JSON_VALUE([GarageSpacesJson].[Value], '$.Deleted') = 'false'
        AND JSON_VALUE([GarageSpacesJson].[Value], '$.ZoneType.Deleted') = 'false'
        AND JSON_VALUE([GarageSpacesJson].[Value], '$.ZoneType.ZoneTypeCode') = 'ZTGarage'
) [garageResult]
OUTER APPLY (
    SELECT TOP(1) JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.Location') AS [Location]
    FROM (
        SELECT *
        FROM OPENJSON((
            SELECT TOP(1) [SpacesJson]
            FROM [dbo].[RSS_AssessmentComplianceBuilding] [building]
            INNER JOIN [dbo].[RSS_AssessmentComplianceOption] [option] ON [building].[AssessmentComplianceOptionId] = [option].[ComplianceOptionsId]
            WHERE [building].[Deleted] = 0
                AND [option].[Deleted] = 0
                AND [option].[AssessmentId] = [job].[CurrentAssessmentId]
                AND [option].[OptionIndex] = 0
                AND [building].[SpacesJson] IS NOT NULL AND [building].[SpacesJson] <> '[]'
        ), '$') AS [ZoneTypeCode]
    ) [OutdoorLivingSpacesJson]
    WHERE JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.Deleted') = 'false'
        AND JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.ZoneType.Deleted') = 'false'
        AND JSON_VALUE([OutdoorLivingSpacesJson].[Value], '$.ZoneType.ZoneTypeCode') = 'ZTOutdoor'
) [outdoorLivingResult]
CROSS APPLY OPENJSON([option].[NCC2022Json]) WITH (
    [IsActive]           BIT '$.IsActive',
    [FlippedFromId]      UNIQUEIDENTIFIER '$.FlippedFromId',
    [Certification]      VARCHAR(200) '$.Certification',
    [AssessmentMethod]   VARCHAR(200) '$.AssessmentMethod',
    [NatHersClimateZone] INT '$.NatHersClimateZone',
    [Ncc2022OffsetList]  NVARCHAR(MAX) '$.Ncc2022OffsetList' AS JSON
) [nccResult]
CROSS APPLY OPENJSON([nccResult].[Ncc2022OffsetList]) [nccOffsetListResult]
LEFT JOIN  [dbo].[RSS_Certification] [certification] ON [nccResult].[Certification] = [certification].[Title] -- Get Certification from NCCResult
WHERE NOT (
        [nccResult].[Certification] = [certification].[Title]
    AND [nccResult].[AssessmentMethod] = [complianceMethod].[Description]
    AND [nccResult].[NatHersClimateZone] = [narHers].[Description]
    AND ([nccOffsetListResult].[key] * 45) = [building].[BuildingOrientation]
);

GO

-- =============================================
-- END OF SCRIPT
-- =============================================

PRINT 'ScriptsForLive.sql execution completed successfully!'
PRINT 'All database migration operations have been applied.'