{"FilesInOrder": null, "AllComplianceOptions": [{"ComplianceMethod": {"PreparedByOverrideUserName": null, "ComplianceMethodCode": "CMPerfSolutionDTS", "Description": "Performance Solution (Comparison with DTS-EP)", "CreatedOn": "2021-03-08T01:20:00+08:00", "CreatedByName": "<PERSON>", "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "RequiresReferenceBuilding": true, "PreparedByOverrideUserId": null}, "AssessmentSoftware": {"AssessmentSoftwareCode": "ASEnergyPlus", "Description": "EnergyPlus", "CreatedOn": "2022-10-17T17:19:16.383+08:00", "CreatedByName": "<PERSON>", "ModifiedOn": "2022-10-17T17:20:02.62+08:00", "ModifiedByName": "<PERSON>", "Deleted": false, "EnergyLoadUnits": "MJ", "SimulationEngine": "CHENATH", "SoftwareVersion": "22.1.0-ed759b17ee", "FileAName": "Assessment File", "FileAExtension": ".frate", "FileARequired": true, "FileBName": "Scratch File", "FileBExtension": null, "FileBRequired": true, "FileCName": "Detailed Zone Map", "FileCExtension": ".txt", "FileCRequired": true, "FileDName": "Output File", "FileDExtension": ".txt", "FileDRequired": true, "FileEName": "Natrep File", "FileEExtension": ".dt2", "FileERequired": true}, "Certification": {"CertificationId": "28b2b2fb-d5f2-455b-90e6-24275892a31b", "Title": "NCC 2019 Building Code of Australia - Volume Two Amendment 1", "Description": null, "Deleted": false, "CreatedOn": "2021-10-06T12:28:00.507+08:00", "CreatedByName": "system", "ModifiedOn": "2022-08-04T16:12:53.297+08:00", "ModifiedByName": "admin", "CHENATHRulesetCode": "NCC 2019", "DecimalStarbandRulesetCode": "NCC 2019", "HeatingAndCoolingRulesetCode": "NCC 2019", "GlazingCalculatorRulesetCode": "NCC 2019", "SectorDeterminationCode": "NCC", "SectorDetermination": {"SectorDeterminationCode": "NCC", "Title": "NCC (8 Sectors)", "SectorsJson": "[\r\n  { \"operator1\": \">\", \"min\": 337.5, \"operator2\": \"<\", \"max\": 360, \"label\": \"N\", \"description\": \"North\"  },\r\n  { \"operator1\": \">=\", \"min\": 0, \"operator2\": \"<=\", \"max\": 22.5, \"label\": \"N\", \"description\": \"North\"  },\r\n  { \"operator1\": \">\", \"min\": 22.5, \"operator2\": \"<=\", \"max\": 67.5, \"label\": \"NE\", \"description\": \"North-East\"  },\r\n  { \"operator1\": \">\", \"min\": 67.5, \"operator2\": \"<=\", \"max\": 112.5, \"label\": \"E\", \"description\": \"East\"  },\r\n  { \"operator1\": \">\", \"min\": 112.5, \"operator2\": \"<=\", \"max\": 157.5, \"label\": \"SE\", \"description\": \"South-East\"  },\r\n  { \"operator1\": \">\", \"min\": 157.5, \"operator2\": \"<=\", \"max\": 202.5, \"label\": \"S\", \"description\": \"South\"  },\r\n  { \"operator1\": \">\", \"min\": 202.5, \"operator2\": \"<=\", \"max\": 247.5, \"label\": \"SW\", \"description\": \"South-West\"  },\r\n  { \"operator1\": \">\", \"min\": 247.5, \"operator2\": \"<=\", \"max\": 292.5, \"label\": \"W\", \"description\": \"West\"  },\r\n  { \"operator1\": \">\", \"min\": 292.5, \"operator2\": \"<=\", \"max\": 337.5, \"label\": \"NW\", \"description\": \"North-West\"  }\r\n]", "Sectors": [{"Operator1": ">", "Min": 337.5, "Operator2": "<", "Max": 360.0, "Label": "N", "Description": "North"}, {"Operator1": ">=", "Min": 0.0, "Operator2": "<=", "Max": 22.5, "Label": "N", "Description": "North"}, {"Operator1": ">", "Min": 22.5, "Operator2": "<=", "Max": 67.5, "Label": "NE", "Description": "North-East"}, {"Operator1": ">", "Min": 67.5, "Operator2": "<=", "Max": 112.5, "Label": "E", "Description": "East"}, {"Operator1": ">", "Min": 112.5, "Operator2": "<=", "Max": 157.5, "Label": "SE", "Description": "South-East"}, {"Operator1": ">", "Min": 157.5, "Operator2": "<=", "Max": 202.5, "Label": "S", "Description": "South"}, {"Operator1": ">", "Min": 202.5, "Operator2": "<=", "Max": 247.5, "Label": "SW", "Description": "South-West"}, {"Operator1": ">", "Min": 247.5, "Operator2": "<=", "Max": 292.5, "Label": "W", "Description": "West"}, {"Operator1": ">", "Min": 292.5, "Operator2": "<=", "Max": 337.5, "Label": "NW", "Description": "North-West"}, {"Operator1": ">", "Min": 337.5, "Operator2": "<", "Max": 360.0, "Label": "N", "Description": "North"}, {"Operator1": ">=", "Min": 0.0, "Operator2": "<=", "Max": 22.5, "Label": "N", "Description": "North"}, {"Operator1": ">", "Min": 22.5, "Operator2": "<=", "Max": 67.5, "Label": "NE", "Description": "North-East"}, {"Operator1": ">", "Min": 67.5, "Operator2": "<=", "Max": 112.5, "Label": "E", "Description": "East"}, {"Operator1": ">", "Min": 112.5, "Operator2": "<=", "Max": 157.5, "Label": "SE", "Description": "South-East"}, {"Operator1": ">", "Min": 157.5, "Operator2": "<=", "Max": 202.5, "Label": "S", "Description": "South"}, {"Operator1": ">", "Min": 202.5, "Operator2": "<=", "Max": 247.5, "Label": "SW", "Description": "South-West"}, {"Operator1": ">", "Min": 247.5, "Operator2": "<=", "Max": 292.5, "Label": "W", "Description": "West"}, {"Operator1": ">", "Min": 292.5, "Operator2": "<=", "Max": 337.5, "Label": "NW", "Description": "North-West"}], "SortOrder": 50, "Deleted": false}}, "SectorDetermination": {"SectorDeterminationCode": "NCC", "Title": "NCC (8 Sectors)", "SectorsJson": "[\r\n  { \"operator1\": \">\", \"min\": 337.5, \"operator2\": \"<\", \"max\": 360, \"label\": \"N\", \"description\": \"North\"  },\r\n  { \"operator1\": \">=\", \"min\": 0, \"operator2\": \"<=\", \"max\": 22.5, \"label\": \"N\", \"description\": \"North\"  },\r\n  { \"operator1\": \">\", \"min\": 22.5, \"operator2\": \"<=\", \"max\": 67.5, \"label\": \"NE\", \"description\": \"North-East\"  },\r\n  { \"operator1\": \">\", \"min\": 67.5, \"operator2\": \"<=\", \"max\": 112.5, \"label\": \"E\", \"description\": \"East\"  },\r\n  { \"operator1\": \">\", \"min\": 112.5, \"operator2\": \"<=\", \"max\": 157.5, \"label\": \"SE\", \"description\": \"South-East\"  },\r\n  { \"operator1\": \">\", \"min\": 157.5, \"operator2\": \"<=\", \"max\": 202.5, \"label\": \"S\", \"description\": \"South\"  },\r\n  { \"operator1\": \">\", \"min\": 202.5, \"operator2\": \"<=\", \"max\": 247.5, \"label\": \"SW\", \"description\": \"South-West\"  },\r\n  { \"operator1\": \">\", \"min\": 247.5, \"operator2\": \"<=\", \"max\": 292.5, \"label\": \"W\", \"description\": \"West\"  },\r\n  { \"operator1\": \">\", \"min\": 292.5, \"operator2\": \"<=\", \"max\": 337.5, \"label\": \"NW\", \"description\": \"North-West\"  }\r\n]", "Sectors": [{"Operator1": ">", "Min": 337.5, "Operator2": "<", "Max": 360.0, "Label": "N", "Description": "North"}, {"Operator1": ">=", "Min": 0.0, "Operator2": "<=", "Max": 22.5, "Label": "N", "Description": "North"}, {"Operator1": ">", "Min": 22.5, "Operator2": "<=", "Max": 67.5, "Label": "NE", "Description": "North-East"}, {"Operator1": ">", "Min": 67.5, "Operator2": "<=", "Max": 112.5, "Label": "E", "Description": "East"}, {"Operator1": ">", "Min": 112.5, "Operator2": "<=", "Max": 157.5, "Label": "SE", "Description": "South-East"}, {"Operator1": ">", "Min": 157.5, "Operator2": "<=", "Max": 202.5, "Label": "S", "Description": "South"}, {"Operator1": ">", "Min": 202.5, "Operator2": "<=", "Max": 247.5, "Label": "SW", "Description": "South-West"}, {"Operator1": ">", "Min": 247.5, "Operator2": "<=", "Max": 292.5, "Label": "W", "Description": "West"}, {"Operator1": ">", "Min": 292.5, "Operator2": "<=", "Max": 337.5, "Label": "NW", "Description": "North-West"}, {"Operator1": ">", "Min": 337.5, "Operator2": "<", "Max": 360.0, "Label": "N", "Description": "North"}, {"Operator1": ">=", "Min": 0.0, "Operator2": "<=", "Max": 22.5, "Label": "N", "Description": "North"}, {"Operator1": ">", "Min": 22.5, "Operator2": "<=", "Max": 67.5, "Label": "NE", "Description": "North-East"}, {"Operator1": ">", "Min": 67.5, "Operator2": "<=", "Max": 112.5, "Label": "E", "Description": "East"}, {"Operator1": ">", "Min": 112.5, "Operator2": "<=", "Max": 157.5, "Label": "SE", "Description": "South-East"}, {"Operator1": ">", "Min": 157.5, "Operator2": "<=", "Max": 202.5, "Label": "S", "Description": "South"}, {"Operator1": ">", "Min": 202.5, "Operator2": "<=", "Max": 247.5, "Label": "SW", "Description": "South-West"}, {"Operator1": ">", "Min": 247.5, "Operator2": "<=", "Max": 292.5, "Label": "W", "Description": "West"}, {"Operator1": ">", "Min": 292.5, "Operator2": "<=", "Max": 337.5, "Label": "NW", "Description": "North-West"}], "SortOrder": 50, "Deleted": false}, "Proposed": {"FileA": null, "FileB": null, "FileC": null, "FileD": null, "FileE": null, "MarkupFile": null, "Zones": [], "DesignFeatures": null, "DO_NOT_PROCESS_DATES": true, "AssessmentComplianceBuildingId": "00000000-0000-0000-0000-000000000000", "AssessmentComplianceOptionId": "00000000-0000-0000-0000-000000000000", "ConstructionTemplateTitle": null, "ConstructionTemplateId": "BLANK_TEMPLATE", "OpeningTemplateTitle": null, "OpeningTemplateId": "BLANK_TEMPLATE", "ServicesTemplateTitle": "No Services", "ServicesTemplateId": "2353ad7a-604a-78fb-b740-3a02654e66c8", "BuildingZonesTemplateId": "BLANK_TEMPLATE", "CreatedOn": "0001-01-01T00:00:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "Description": null, "HouseEnergyRating": null, "HouseEnergyRatingOverride": null, "Heating": null, "Cooling": null, "TotalEnergyLoad": null, "HeatingOriginal": null, "CoolingOriginal": null, "TotalEnergyLoadOriginal": null, "OverrideEnergyLoads": false, "HeatingLoadLimitCorrectionFactor": null, "CoolingLoadLimitCorrectionFactor": null, "Ncc2019AreaCorrectionFactor": null, "Ncc2022AreaCorrectionFactor": null, "ConditionedFloorArea": null, "UnconditionedFloorArea": null, "AttachedGarageFloorArea": null, "BuildingOrientation": null, "Classification": null, "OpeningSpecification": null, "LowestLivingAreaFloorType": null, "MasonryWalls": null, "GarageLocation": null, "BuildingWidth": null, "BuildingLength": null, "BuildingPerimeter": null, "RoofArea": null, "ProjectDescription": {"ProjectDescriptionCode": "BDSingle", "Description": "Single Storey", "ProjectRatingModeId": null, "ProjectRatingModeDescription": null, "CreatedOn": "2021-03-23T12:00:00.1+08:00", "CreatedByName": "admin", "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "SortOrder": 1, "NumberOfStoreysWithGlazing": 1, "DefaultForStoreyCount": true, "Storeys": null}, "ProjectDescriptionOther": null, "ProjectClassification": null, "DesignWasBlankFromTemplate": null, "Design": null, "FileAId": null, "FileBId": null, "FileCId": null, "FileDId": null, "FileEId": null, "MarkupFileId": null, "IncludeBuildingElementsInReport": null, "Surfaces": [], "Openings": [], "Services": [], "Storeys": [{"Name": "Ground Floor", "Description": null, "Floor": 0, "HeightOffGround": null, "FloorArea": null, "CeilingArea": null}], "CategoriesWithExternalData": {}, "CategoriesNotRequired": {"spaceheatingsystem": true, "spacecoolingsystem": true, "hotwatersystem": true, "cooktop": false, "oven": false, "artificiallighting": true, "exhaustfans": true, "ceilingvents": true, "ceilingfans": true, "photovoltaicsystem": true, "swimmingpool": true, "spa": true}, "ZoneTypesNotApplicable": {"floorPlanSpaces": false, "generalroofs": false}, "EnergyUsageSummary": null, "Spaces": [{"ZoneId": "f15cd87a-4bcb-4127-ad7e-00fa8cceb37d", "AssessmentComplianceBuildingId": "00000000-0000-0000-0000-000000000000", "LinkId": "05a1ca6c-ab21-47a7-b64b-e8b0ca52565d", "CreatedOn": "2025-07-10T09:06:18+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "SortOrder": null, "ZoneNumber": "A001", "ZoneNumberSource": "AUTO", "ZoneDescription": "House", "OriginalZoneName": null, "ZoneType": {"ZoneTypeCode": "ZTHouse", "Description": "House", "SortOrder": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "LampPowerMaximumWM2": 5.0, "DefaultNccClassificationCode": "Class10A", "AvailableFor": "spaces"}, "ZoneActivity": null, "NccClassification": null, "CeilingArea": null, "FloorArea": null, "RoofArea": null, "Volume": null, "Storey": 0, "Conditioned": null, "RoofLight": null, "CeilingFan": null, "EvaporativeCooler": null, "NaturallyVentilated": null, "NaturalLightRequiredPercent": null, "NaturalLightRequiredM2": null, "NaturalLightAchievedM2": null, "VentilationRequiredPercent": null, "VentilationRequiredM2": null, "VentilationAchievedM2": null, "AirMovementRequiredPercent": null, "AirMovementRequiredM2": null, "AirMovementAchievedM2": null, "LampPowerMaximumWM2": null, "LampPowerMaximumW": null, "LampPowerAchievedW": null, "StoreyBelow": null, "StoreyAbove": null, "IsReflective": null, "AirCavity": null, "ConditionedFloorArea": null, "Perimeter": null, "WallThickness": null, "Conductivity": null, "Diffusivity": null, "GroundReflectance": null, "EdgeInsulation": null, "CeilingHeight": null, "RoofPitch": null, "Location": "Other", "ExteriorWallArea": null, "ExteriorGlazingArea": null, "FloorAreaPercent": null, "VolumePercent": null, "ExteriorWallAreaPercent": null, "ExteriorGlazingAreaPercent": null, "GlassExteriorWallAreaPercent": null, "GlassFloorAreaPercent": null, "ZoneCount": 0}, {"ZoneId": "cbb60014-733a-43ea-a12f-4be6e8850385", "AssessmentComplianceBuildingId": "00000000-0000-0000-0000-000000000000", "LinkId": "bc1854ab-e8c7-45ab-b526-88137f104387", "CreatedOn": "2025-07-10T09:06:18+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "SortOrder": null, "ZoneNumber": "A002", "ZoneNumberSource": "AUTO", "ZoneDescription": "Garage", "OriginalZoneName": null, "ZoneType": {"ZoneTypeCode": "ZTGarage", "Description": "Garage", "SortOrder": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "LampPowerMaximumWM2": 5.0, "DefaultNccClassificationCode": "Class10A", "AvailableFor": "spaces"}, "ZoneActivity": null, "NccClassification": null, "CeilingArea": null, "FloorArea": null, "RoofArea": null, "Volume": null, "Storey": 0, "Conditioned": null, "RoofLight": null, "CeilingFan": null, "EvaporativeCooler": null, "NaturallyVentilated": null, "NaturalLightRequiredPercent": null, "NaturalLightRequiredM2": null, "NaturalLightAchievedM2": null, "VentilationRequiredPercent": null, "VentilationRequiredM2": null, "VentilationAchievedM2": null, "AirMovementRequiredPercent": null, "AirMovementRequiredM2": null, "AirMovementAchievedM2": null, "LampPowerMaximumWM2": null, "LampPowerMaximumW": null, "LampPowerAchievedW": null, "StoreyBelow": null, "StoreyAbove": null, "IsReflective": null, "AirCavity": null, "ConditionedFloorArea": null, "Perimeter": null, "WallThickness": null, "Conductivity": null, "Diffusivity": null, "GroundReflectance": null, "EdgeInsulation": null, "CeilingHeight": null, "RoofPitch": null, "Location": null, "ExteriorWallArea": null, "ExteriorGlazingArea": null, "FloorAreaPercent": null, "VolumePercent": null, "ExteriorWallAreaPercent": null, "ExteriorGlazingAreaPercent": null, "GlassExteriorWallAreaPercent": null, "GlassFloorAreaPercent": null, "ZoneCount": 0}, {"ZoneId": "5b7a025e-4107-4213-908c-e86cd239c1c3", "AssessmentComplianceBuildingId": "00000000-0000-0000-0000-000000000000", "LinkId": "3e080f3d-4ef0-4d95-85e3-64d4e290043e", "CreatedOn": "2025-07-10T09:06:18+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "SortOrder": null, "ZoneNumber": "A003", "ZoneNumberSource": "AUTO", "ZoneDescription": "Alfresco", "OriginalZoneName": null, "ZoneType": {"ZoneTypeCode": "ZTOutdoor", "Description": "Covered Outdoor Living", "SortOrder": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "LampPowerMaximumWM2": 4.0, "DefaultNccClassificationCode": "Class10A", "AvailableFor": "spaces"}, "ZoneActivity": null, "NccClassification": null, "CeilingArea": null, "FloorArea": null, "RoofArea": null, "Volume": null, "Storey": 0, "Conditioned": null, "RoofLight": null, "CeilingFan": null, "EvaporativeCooler": null, "NaturallyVentilated": null, "NaturalLightRequiredPercent": null, "NaturalLightRequiredM2": null, "NaturalLightAchievedM2": null, "VentilationRequiredPercent": null, "VentilationRequiredM2": null, "VentilationAchievedM2": null, "AirMovementRequiredPercent": null, "AirMovementRequiredM2": null, "AirMovementAchievedM2": null, "LampPowerMaximumWM2": null, "LampPowerMaximumW": null, "LampPowerAchievedW": null, "StoreyBelow": null, "StoreyAbove": null, "IsReflective": null, "AirCavity": null, "ConditionedFloorArea": null, "Perimeter": null, "WallThickness": null, "Conductivity": null, "Diffusivity": null, "GroundReflectance": null, "EdgeInsulation": null, "CeilingHeight": null, "RoofPitch": null, "Location": null, "ExteriorWallArea": null, "ExteriorGlazingArea": null, "FloorAreaPercent": null, "VolumePercent": null, "ExteriorWallAreaPercent": null, "ExteriorGlazingAreaPercent": null, "GlassExteriorWallAreaPercent": null, "GlassFloorAreaPercent": null, "ZoneCount": 0}, {"ZoneId": "dcfc1664-d101-4741-8ad8-6682c007b3f8", "AssessmentComplianceBuildingId": "00000000-0000-0000-0000-000000000000", "LinkId": "8cb85fd4-fbe1-42cf-bec0-d171915981be", "CreatedOn": "2025-07-10T09:06:18+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "SortOrder": null, "ZoneNumber": "A004", "ZoneNumberSource": "AUTO", "ZoneDescription": "Porch", "OriginalZoneName": null, "ZoneType": {"ZoneTypeCode": "ZTPorch", "Description": "Porch", "SortOrder": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "LampPowerMaximumWM2": 5.0, "DefaultNccClassificationCode": "Class10A", "AvailableFor": "spaces"}, "ZoneActivity": null, "NccClassification": null, "CeilingArea": null, "FloorArea": null, "RoofArea": null, "Volume": null, "Storey": 0, "Conditioned": null, "RoofLight": null, "CeilingFan": null, "EvaporativeCooler": null, "NaturallyVentilated": null, "NaturalLightRequiredPercent": null, "NaturalLightRequiredM2": null, "NaturalLightAchievedM2": null, "VentilationRequiredPercent": null, "VentilationRequiredM2": null, "VentilationAchievedM2": null, "AirMovementRequiredPercent": null, "AirMovementRequiredM2": null, "AirMovementAchievedM2": null, "LampPowerMaximumWM2": null, "LampPowerMaximumW": null, "LampPowerAchievedW": null, "StoreyBelow": null, "StoreyAbove": null, "IsReflective": null, "AirCavity": null, "ConditionedFloorArea": null, "Perimeter": null, "WallThickness": null, "Conductivity": null, "Diffusivity": null, "GroundReflectance": null, "EdgeInsulation": null, "CeilingHeight": null, "RoofPitch": null, "Location": null, "ExteriorWallArea": null, "ExteriorGlazingArea": null, "FloorAreaPercent": null, "VolumePercent": null, "ExteriorWallAreaPercent": null, "ExteriorGlazingAreaPercent": null, "GlassExteriorWallAreaPercent": null, "GlassFloorAreaPercent": null, "ZoneCount": 0}], "Roofs": [{"ZoneId": "92c1680d-1b81-46ed-8170-9bd5183a190a", "AssessmentComplianceBuildingId": "00000000-0000-0000-0000-000000000000", "LinkId": "30217f96-194b-4b12-adb0-62fc56a628ae", "CreatedOn": "2025-07-10T09:06:18+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "SortOrder": null, "ZoneNumber": "R001", "ZoneNumberSource": "AUTO", "ZoneDescription": "<PERSON><PERSON>", "OriginalZoneName": null, "ZoneType": null, "ZoneActivity": null, "NccClassification": null, "CeilingArea": null, "FloorArea": null, "RoofArea": null, "Volume": null, "Storey": 0, "Conditioned": null, "RoofLight": null, "CeilingFan": null, "EvaporativeCooler": null, "NaturallyVentilated": null, "NaturalLightRequiredPercent": null, "NaturalLightRequiredM2": null, "NaturalLightAchievedM2": null, "VentilationRequiredPercent": null, "VentilationRequiredM2": null, "VentilationAchievedM2": null, "AirMovementRequiredPercent": null, "AirMovementRequiredM2": null, "AirMovementAchievedM2": null, "LampPowerMaximumWM2": null, "LampPowerMaximumW": null, "LampPowerAchievedW": null, "StoreyBelow": null, "StoreyAbove": null, "IsReflective": null, "AirCavity": null, "ConditionedFloorArea": null, "Perimeter": null, "WallThickness": null, "Conductivity": null, "Diffusivity": null, "GroundReflectance": null, "EdgeInsulation": null, "CeilingHeight": null, "RoofPitch": null, "Location": null, "ExteriorWallArea": null, "ExteriorGlazingArea": null, "FloorAreaPercent": null, "VolumePercent": null, "ExteriorWallAreaPercent": null, "ExteriorGlazingAreaPercent": null, "GlassExteriorWallAreaPercent": null, "GlassFloorAreaPercent": null, "ZoneCount": 0}], "EnergyResultsChartData": null, "EnergyResultsChartDataJson": null, "EnvelopeSummary": null, "EnvelopeSummaryConditioned": null, "EnvelopeSummaryHabitable": null, "ZoneSummary": null}, "Reference": {"FileA": null, "FileB": null, "FileC": null, "FileD": null, "FileE": null, "MarkupFile": null, "Zones": [], "DesignFeatures": null, "DO_NOT_PROCESS_DATES": true, "AssessmentComplianceBuildingId": "00000000-0000-0000-0000-000000000000", "AssessmentComplianceOptionId": "00000000-0000-0000-0000-000000000000", "ConstructionTemplateTitle": null, "ConstructionTemplateId": "BLANK_TEMPLATE", "OpeningTemplateTitle": null, "OpeningTemplateId": "BLANK_TEMPLATE", "ServicesTemplateTitle": "No Services", "ServicesTemplateId": "2353ad7a-604a-78fb-b740-3a02654e66c8", "BuildingZonesTemplateId": "BLANK_TEMPLATE", "CreatedOn": "0001-01-01T00:00:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "Description": null, "HouseEnergyRating": null, "HouseEnergyRatingOverride": null, "Heating": null, "Cooling": null, "TotalEnergyLoad": null, "HeatingOriginal": null, "CoolingOriginal": null, "TotalEnergyLoadOriginal": null, "OverrideEnergyLoads": false, "HeatingLoadLimitCorrectionFactor": null, "CoolingLoadLimitCorrectionFactor": null, "Ncc2019AreaCorrectionFactor": null, "Ncc2022AreaCorrectionFactor": null, "ConditionedFloorArea": null, "UnconditionedFloorArea": null, "AttachedGarageFloorArea": null, "BuildingOrientation": null, "Classification": null, "OpeningSpecification": null, "LowestLivingAreaFloorType": null, "MasonryWalls": null, "GarageLocation": null, "BuildingWidth": null, "BuildingLength": null, "BuildingPerimeter": null, "RoofArea": null, "ProjectDescription": null, "ProjectDescriptionOther": null, "ProjectClassification": null, "DesignWasBlankFromTemplate": null, "Design": null, "FileAId": null, "FileBId": null, "FileCId": null, "FileDId": null, "FileEId": null, "MarkupFileId": null, "IncludeBuildingElementsInReport": null, "Surfaces": [], "Openings": [], "Services": [], "Storeys": [], "CategoriesWithExternalData": {}, "CategoriesNotRequired": {"spaceheatingsystem": true, "spacecoolingsystem": true, "hotwatersystem": true, "cooktop": false, "oven": false, "artificiallighting": true, "exhaustfans": true, "ceilingvents": true, "ceilingfans": true, "photovoltaicsystem": true, "swimmingpool": true, "spa": true}, "ZoneTypesNotApplicable": {"floorPlanSpaces": true, "generalroofs": true}, "EnergyUsageSummary": null, "Spaces": null, "Roofs": null, "EnergyResultsChartData": null, "EnergyResultsChartDataJson": null, "EnvelopeSummary": null, "EnvelopeSummaryConditioned": null, "EnvelopeSummaryHabitable": null, "ZoneSummary": null}, "PurchaseOrderFile": null, "AssessmentDrawings": [], "CreatedOn": "0001-01-01T00:00:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "ComplianceOptionsId": "0a33a3af-0955-4c6c-85be-f269745e2f3d", "AssessmentId": "0a06fc0c-7469-4b74-a2d9-bdb1181ad498", "Description": "Baseline specifications", "ComplianceMethodCode": "CMPerfSolutionDTS", "ComplianceMethodDescription": null, "RequiredHouseEnergyRating": -1.0, "ProposedBuildingId": null, "ReferenceBuildingId": null, "PurchaseOrder": null, "PurchaseOrderFileId": null, "SectorDeterminationCode": null, "OptionIndex": 0, "IsBaselineSimulation": true, "IsSelected": true, "IsCompliant": false, "IsComplianceValid": true, "IsShownToClient": false, "IsLocked": false, "AssessmentSoftwareCode": null, "UpdatedDrawingsRequired": null, "NewPurchaseOrderRequired": false, "MarkupFileRequired": false, "HeatingAndCoolingRulesetCode": "Enabled", "ItemReference": null, "EPComplianceData": null, "ncc2022": null}], "PerformanceRequirementP261Description": "Satisfied in accordance with Performance Solution A2.2(2)(d)", "Job": {"Assessments": null, "FilesInOrder": [], "CurrentAssessment": null, "Client": {"ClientDefault": {"AssessorUser": {"UserId": "414d94ea-dda3-4464-bc35-e4109267fa21", "AspNetUserId": "20F30EAB-E769-4D19-AF96-7478CDF50F67", "LoginEnabled": true, "IsExternal": false, "FullName": "<PERSON>", "FirstName": "<PERSON>", "LastName": "<PERSON>", "EmailAddress": "<EMAIL>", "Phone": null, "MobilePhone": null, "Deleted": false, "BusinessUnitId": null, "BusinessUnitName": null, "JobTitle": null, "CreatedOn": "2022-10-18T14:47:22.54+08:00", "CreatedByName": "<PERSON>", "CreatedBy": "00000000-0000-0000-0000-000000000000", "ModifiedOn": "2022-11-16T04:52:11.397+08:00", "ModifiedByName": "Thermarate Admin", "ModifiedBy": null, "ClientId": null, "PrimaryRoleCode": "Assessor", "PrimaryRoleDescription": "Assessor", "SignatureSVGImage": "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" width=\"283.465\" height=\"170\"><g transform=\"scale(0.23529411764705882)\"><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 20 19 c -0.03 0.07 -1.81 2.68 -2 4 c -0.42 2.95 -0.36 6.79 0 10 c 0.29 2.64 0.92 6.2 2 8 c 0.58 0.97 2.62 1.58 4 2 c 2.84 0.87 6.65 2.59 9 2 c 3.32 -0.83 7.81 -4.41 11 -7 c 1.91 -1.55 3.5 -3.84 5 -6 c 3.89 -5.59 7.85 -11.49 11 -17 c 0.64 -1.12 0.42 -2.83 1 -4 c 1.65 -3.31 5.91 -9.82 6 -10 c 0.04 -0.08 -2.28 2.56 -3 4 c -1.87 3.75 -3.47 7.81 -5 12 c -5 13.67 -9.65 26.67 -14 40 c -0.62 1.91 -0.29 4.16 -1 6 c -4.17 10.83 -9.78 21.61 -14 33 c -3.58 9.65 -6.38 19 -9 29 c -2.83 10.79 -4.31 21.25 -7 32 l -4 12\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 80 26 c 0.14 -0.14 5.14 -5.64 8 -8 c 2.74 -2.26 5.9 -4.67 9 -6 c 3.54 -1.52 8.31 -2.57 12 -3 c 1.52 -0.18 3.68 0.28 5 1 c 2.02 1.1 5.37 3.23 6 5 c 0.73 2.05 -0.13 6.27 -1 9 c -1.68 5.26 -4.38 10.62 -7 16 c -3.89 7.98 -7.38 15.82 -12 23 c -4.31 6.7 -9.75 12.75 -15 19 c -1.84 2.19 -3.8 4.22 -6 6 c -4.81 3.9 -10.57 6.85 -15 11 c -5.74 5.37 -10.45 12.2 -16 18 c -1.8 1.88 -3.86 3.72 -6 5 c -2.7 1.62 -5.9 3.01 -9 4 c -5.19 1.66 -11.63 4 -16 4 c -2.14 0 -4.66 -2.93 -7 -4 c -1.2 -0.55 -3.17 -0.27 -4 -1 c -1.45 -1.27 -2.99 -3.99 -4 -6 c -0.57 -1.15 -0.91 -2.67 -1 -4 c -0.24 -3.54 -0.52 -7.49 0 -11 c 0.78 -5.26 2.18 -11.13 4 -16 c 1.04 -2.78 3.01 -5.74 5 -8 c 2.84 -3.22 6.57 -6.1 10 -9 c 0.92 -0.78 1.96 -1.65 3 -2 c 1.75 -0.58 4.15 -1 6 -1 c 1.25 0 2.87 0.36 4 1 c 6.15 3.48 12.97 7.64 19 12 c 3.61 2.61 7.21 5.82 10 9 c 1.67 1.91 2.94 4.53 4 7 c 3.96 9.23 6.97 19.17 11 28 c 1.15 2.52 2.96 5.93 5 7 c 3.39 1.78 9.38 2.27 14 3 c 1.61 0.25 3.58 0.47 5 0 l 10 -5\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 95 26 c -0.05 0.19 -1.58 7.48 -3 11 c -4.92 12.16 -10.93 23.51 -16 36 c -4.52 11.14 -7.88 22.42 -12 33 c -0.42 1.09 -1.64 1.93 -2 3 c -1.54 4.62 -3.07 10.11 -4 15 l 0 6\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 99 94 c -0.09 0.3 -3.68 11.29 -5 17 c -0.67 2.92 -1.63 7.25 -1 9 c 0.38 1.04 3.38 1.68 5 2 c 1.51 0.3 3.61 0.46 5 0 c 2.23 -0.74 5.08 -2.38 7 -4 c 2.19 -1.86 4.43 -4.47 6 -7 c 2.66 -4.29 4.91 -9.2 7 -14 c 1.26 -2.91 1.71 -6.25 3 -9 c 0.98 -2.09 4.06 -6.17 4 -6 c -0.31 0.95 -8.68 24.58 -14 37 c -6.98 16.29 -22 47 -22 47\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 136 71 c 0 0.1 -0.41 4.22 0 6 c 0.52 2.26 1.53 5.67 3 7 c 1.64 1.49 5.74 3 8 3 c 2.02 0 6.77 -2.54 7 -3 c 0.15 -0.31 -3.76 -1.52 -5 -1 c -4.05 1.71 -11.35 5.12 -14 9 c -5 7.31 -8.41 19.26 -12 29 c -1.05 2.86 -2 6.6 -2 9 c 0 0.94 1.35 3.15 2 3 c 2.26 -0.52 7.95 -3.89 11 -6 c 0.88 -0.61 1.18 -2.18 2 -3 c 2.41 -2.41 5.87 -4.41 8 -7 c 2.34 -2.84 4.41 -6.52 6 -10 c 2.03 -4.43 5.12 -14.23 5 -14 c -0.2 0.41 -7.23 15.89 -10 24 c -1.53 4.46 -2.27 9.38 -3 14 c -0.25 1.61 -0.67 4.44 0 5 c 0.81 0.67 4.4 0.48 6 0 c 1.33 -0.4 2.86 -1.86 4 -3 c 1.46 -1.46 2.73 -3.23 4 -5 c 2.14 -3 4.47 -5.82 6 -9 c 2.73 -5.67 6.21 -17.89 7 -18 c 0.7 -0.1 -0.25 11.43 0 17 c 0.08 1.67 0.48 3.45 1 5 c 0.45 1.36 1.12 3.12 2 4 c 0.88 0.88 2.7 1.71 4 2 c 1.46 0.32 3.49 0.35 5 0 c 2.58 -0.6 6.31 -1.24 8 -3 l 16 -22\"/></g></svg>", "SignatureBase64Image": null, "UserName": "<PERSON>", "Password": null, "IsGoogleLinked": false, "IsMicrosoftLinked": false}, "ClientDefaultId": "38aa1885-a2b0-baa4-30c6-3a06fbad0c32", "ClientId": "00000000-0000-0000-0000-000000000000", "CreatedOn": "2022-10-18T13:10:36.85+08:00", "CreatedByName": "<PERSON>", "ModifiedOn": "2022-10-24T18:29:38.76+08:00", "ModifiedByName": "Thermarate Admin", "Deleted": false, "PreliminaryComplianceMethodCode": "CMPerfSolutionDTS", "AssessmentSoftwareCode": "ASEnergyPlus", "MinHouseEnergyRating": null, "PriorityCode": "NORMAL", "CertificationId": "28b2b2fb-d5f2-455b-90e6-24275892a31b", "AssessorUserId": "414d94ea-dda3-4464-bc35-e4109267fa21", "BuildingExposureCode": "BESuburban", "PurchaseOrderCode": "NotRequired", "IncludeDrawingsInReport": true, "StampDrawings": true, "EmailAssessorNewJob": null, "EmailAssessorRecertification": null, "EmailAssessorCopyAssessment": null, "NominatedBuildingSurveyorId": "b9ca3679-5ad6-4a25-9afe-f79c5131cfdb", "WorksDescription": {"WorksDescriptionCode": "WDNewBuild", "Description": "New Dwelling", "CreatedOn": "2021-03-22T12:00:00.1+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "SortOrder": 1, "Deleted": false}, "ProposedConstructionTemplateId": "BLANK_TEMPLATE", "ReferenceConstructionTemplateId": "BLANK_TEMPLATE", "ProposedOpeningTemplateId": "BLANK_TEMPLATE", "ReferenceOpeningTemplateId": "BLANK_TEMPLATE", "ProposedServicesTemplateId": "2353ad7a-604a-78fb-b740-3a02654e66c8", "ReferenceServicesTemplateId": "2353ad7a-604a-78fb-b740-3a02654e66c8", "BuildingZonesTemplateId": "BLANK_TEMPLATE", "ReferenceBuildingZonesTemplateId": "BLANK_TEMPLATE", "AssessorNotesNotApplicable": true, "AssessorNotes": null, "ProjectDescription": null, "NccBuildingClassification": null, "SpaceHeatingServiceTypeCode": null, "SpaceHeatingGems2019Rating": null, "SpaceCoolingServiceTypeCode": null, "SpaceCoolingGems2019Rating": null, "WaterHeatingServiceTypeCode": null, "WaterHeatingGems2019Rating": null, "SwimmingPoolExists": null, "SwimmingPoolVolume": null, "SwimmingPoolGems2019Rating": null, "SpaExists": null, "SpaVolume": null, "SpaGems2019Rating": null, "PhotovoltaicExists": null, "PhotovoltaicCapacity": null}, "UsersCount": 2, "JobsCount": 438, "JobsActiveCount": 0, "EnergyLabsIsActive": false, "ProjectsActiveCount": 0, "HomeDesignsActiveCount": 0, "HomeDesignVariationsActiveCount": 0, "ClientId": "f1aeb4db-e325-ba98-4ac9-3a06fbad0c32", "ClientName": "Affordable Living Homes", "CreatedOn": "2022-10-18T13:10:36.897+08:00", "CreatedByName": "<PERSON>", "ModifiedOn": "2022-10-24T18:29:38.76+08:00", "ModifiedByName": "Thermarate Admin", "Deleted": false, "DefaultClientAssigneeUserId": "b4c8477c-ff2f-406c-bc53-7a46c1429bdc", "AccountsFirstName": "<PERSON>", "AccountsLastName": "<PERSON><PERSON><PERSON>", "AccountsPhone": "(08) 6200 0272", "AccountsEmail": "<EMAIL>", "AccountsNote": null, "AccountsSameAsContact": false, "HasClientPortalAccess": true, "IsFavourite": false, "ClientOptions": {"AvailableBuildingSurveyorIds": ["b9ca3679-5ad6-4a25-9afe-f79c5131cfdb", "2502eb9c-cc6d-4711-b13c-a57bec57e9ab"], "AvailableComplianceMethodCodes": ["CMHouseEnergyRating", "CMPerfSolutionDTS"], "AvailableHouseEnergyRatings": ["6.0", "6.5", "7.0", "7.5", "8.0", "8.5", "9.0", "9.5", "10.0"], "AvailableCertifications": ["28b2b2fb-d5f2-455b-90e6-24275892a31b"], "AvailableWorksDescriptions": ["WDNewBuild"], "HeatingAndCoolingRulesetCode": "Inherit", "IncludeDrawingsInReport": false, "StampDrawings": false, "ComplianceCostEnabled": false, "Ncc2022ThermPerfEnabled": false, "Ncc2022WohEnabled": false, "AnalyticsEnabled": false}, "ReportSettings": {"SpecificationSummaryReports": {"Baseline": {"ShowOnClientPortal": true, "AllowDownloadOnClientPortal": false}, "ComplianceOption": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}}, "ComplianceReports": {"PreliminaryReport": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}, "EnergyEfficiencyPbdb": {"ShowOnClientPortal": true, "AllowDownloadOnClientPortal": true}, "EnergyEfficiencyComplianceReport": {"ShowOnClientPortal": true, "AllowDownloadOnClientPortal": true}, "LightAndVentilationReport": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}, "SoftwareReport": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}, "Specification44Report": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}}}, "ClientCostItems": []}, "AssessorUser": {"BusinessUnit": null, "PrimaryRole": {"RoleCode": "Assessor", "Description": "Assessor", "Deleted": false}, "Client": null, "UserId": "414d94ea-dda3-4464-bc35-e4109267fa21", "AspNetUserId": "20F30EAB-E769-4D19-AF96-7478CDF50F67", "LoginEnabled": true, "IsExternal": false, "FullName": "<PERSON>", "FirstName": "<PERSON>", "LastName": "<PERSON>", "EmailAddress": "<EMAIL>", "Phone": null, "MobilePhone": null, "Deleted": false, "BusinessUnitId": null, "BusinessUnitName": null, "JobTitle": null, "CreatedOn": "2022-10-18T14:47:22.54+08:00", "CreatedByName": "<PERSON>", "CreatedBy": "00000000-0000-0000-0000-000000000000", "ModifiedOn": "2022-11-16T04:52:11.397+08:00", "ModifiedByName": "Thermarate Admin", "ModifiedBy": null, "ClientId": null, "PrimaryRoleCode": "Assessor", "PrimaryRoleDescription": "Assessor", "SignatureSVGImage": "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" width=\"283.465\" height=\"170\"><g transform=\"scale(0.23529411764705882)\"><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 20 19 c -0.03 0.07 -1.81 2.68 -2 4 c -0.42 2.95 -0.36 6.79 0 10 c 0.29 2.64 0.92 6.2 2 8 c 0.58 0.97 2.62 1.58 4 2 c 2.84 0.87 6.65 2.59 9 2 c 3.32 -0.83 7.81 -4.41 11 -7 c 1.91 -1.55 3.5 -3.84 5 -6 c 3.89 -5.59 7.85 -11.49 11 -17 c 0.64 -1.12 0.42 -2.83 1 -4 c 1.65 -3.31 5.91 -9.82 6 -10 c 0.04 -0.08 -2.28 2.56 -3 4 c -1.87 3.75 -3.47 7.81 -5 12 c -5 13.67 -9.65 26.67 -14 40 c -0.62 1.91 -0.29 4.16 -1 6 c -4.17 10.83 -9.78 21.61 -14 33 c -3.58 9.65 -6.38 19 -9 29 c -2.83 10.79 -4.31 21.25 -7 32 l -4 12\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 80 26 c 0.14 -0.14 5.14 -5.64 8 -8 c 2.74 -2.26 5.9 -4.67 9 -6 c 3.54 -1.52 8.31 -2.57 12 -3 c 1.52 -0.18 3.68 0.28 5 1 c 2.02 1.1 5.37 3.23 6 5 c 0.73 2.05 -0.13 6.27 -1 9 c -1.68 5.26 -4.38 10.62 -7 16 c -3.89 7.98 -7.38 15.82 -12 23 c -4.31 6.7 -9.75 12.75 -15 19 c -1.84 2.19 -3.8 4.22 -6 6 c -4.81 3.9 -10.57 6.85 -15 11 c -5.74 5.37 -10.45 12.2 -16 18 c -1.8 1.88 -3.86 3.72 -6 5 c -2.7 1.62 -5.9 3.01 -9 4 c -5.19 1.66 -11.63 4 -16 4 c -2.14 0 -4.66 -2.93 -7 -4 c -1.2 -0.55 -3.17 -0.27 -4 -1 c -1.45 -1.27 -2.99 -3.99 -4 -6 c -0.57 -1.15 -0.91 -2.67 -1 -4 c -0.24 -3.54 -0.52 -7.49 0 -11 c 0.78 -5.26 2.18 -11.13 4 -16 c 1.04 -2.78 3.01 -5.74 5 -8 c 2.84 -3.22 6.57 -6.1 10 -9 c 0.92 -0.78 1.96 -1.65 3 -2 c 1.75 -0.58 4.15 -1 6 -1 c 1.25 0 2.87 0.36 4 1 c 6.15 3.48 12.97 7.64 19 12 c 3.61 2.61 7.21 5.82 10 9 c 1.67 1.91 2.94 4.53 4 7 c 3.96 9.23 6.97 19.17 11 28 c 1.15 2.52 2.96 5.93 5 7 c 3.39 1.78 9.38 2.27 14 3 c 1.61 0.25 3.58 0.47 5 0 l 10 -5\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 95 26 c -0.05 0.19 -1.58 7.48 -3 11 c -4.92 12.16 -10.93 23.51 -16 36 c -4.52 11.14 -7.88 22.42 -12 33 c -0.42 1.09 -1.64 1.93 -2 3 c -1.54 4.62 -3.07 10.11 -4 15 l 0 6\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 99 94 c -0.09 0.3 -3.68 11.29 -5 17 c -0.67 2.92 -1.63 7.25 -1 9 c 0.38 1.04 3.38 1.68 5 2 c 1.51 0.3 3.61 0.46 5 0 c 2.23 -0.74 5.08 -2.38 7 -4 c 2.19 -1.86 4.43 -4.47 6 -7 c 2.66 -4.29 4.91 -9.2 7 -14 c 1.26 -2.91 1.71 -6.25 3 -9 c 0.98 -2.09 4.06 -6.17 4 -6 c -0.31 0.95 -8.68 24.58 -14 37 c -6.98 16.29 -22 47 -22 47\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 136 71 c 0 0.1 -0.41 4.22 0 6 c 0.52 2.26 1.53 5.67 3 7 c 1.64 1.49 5.74 3 8 3 c 2.02 0 6.77 -2.54 7 -3 c 0.15 -0.31 -3.76 -1.52 -5 -1 c -4.05 1.71 -11.35 5.12 -14 9 c -5 7.31 -8.41 19.26 -12 29 c -1.05 2.86 -2 6.6 -2 9 c 0 0.94 1.35 3.15 2 3 c 2.26 -0.52 7.95 -3.89 11 -6 c 0.88 -0.61 1.18 -2.18 2 -3 c 2.41 -2.41 5.87 -4.41 8 -7 c 2.34 -2.84 4.41 -6.52 6 -10 c 2.03 -4.43 5.12 -14.23 5 -14 c -0.2 0.41 -7.23 15.89 -10 24 c -1.53 4.46 -2.27 9.38 -3 14 c -0.25 1.61 -0.67 4.44 0 5 c 0.81 0.67 4.4 0.48 6 0 c 1.33 -0.4 2.86 -1.86 4 -3 c 1.46 -1.46 2.73 -3.23 4 -5 c 2.14 -3 4.47 -5.82 6 -9 c 2.73 -5.67 6.21 -17.89 7 -18 c 0.7 -0.1 -0.25 11.43 0 17 c 0.08 1.67 0.48 3.45 1 5 c 0.45 1.36 1.12 3.12 2 4 c 0.88 0.88 2.7 1.71 4 2 c 1.46 0.32 3.49 0.35 5 0 c 2.58 -0.6 6.31 -1.24 8 -3 l 16 -22\"/></g></svg>", "SignatureBase64Image": null, "UserName": "<PERSON>", "Password": null, "IsGoogleLinked": false, "IsMicrosoftLinked": false}, "Status": {"StatusCode": "JDraft", "Description": "Processing (New Job)", "Deleted": false, "SortOrder": 9999, "Hidden": false, "StatusTypeCode": null}, "JobEvents": null, "JobId": "7acf15e6-12f2-df4b-5b88-3a1b040b51ac", "ClientId": "00000000-0000-0000-0000-000000000000", "ClientName": null, "JobReference": "J4839", "AssessorUserId": null, "AssessorFullName": null, "StatusCode": "JDraft", "StatusDescription": null, "Notes": null, "CreatedOn": "2025-07-10T09:06:19.1313072+08:00", "CreatedByName": "Support Support", "ModifiedOn": "2025-07-10T09:06:19.1313072+08:00", "ModifiedByName": null, "Deleted": false, "CurrentAssessmentId": null}, "Status": {"StatusCode": "ADraft", "Description": "Processing (New Job)", "Deleted": false, "SortOrder": 10, "Hidden": false, "StatusTypeCode": null}, "AssessmentSoftware": null, "NCCClimateZone": {"NCCClimateZoneCode": "NCC4", "Description": "4", "CreatedOn": "2016-10-25T17:31:03.963+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false}, "NatHERSClimateZone": {"NatHERSClimateZoneCode": "Nat48", "Description": "48", "CreatedOn": "2016-12-16T12:23:24.68+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false}, "BuildingExposure": null, "PerformanceRequirementP261": null, "PerformanceRequirementP262": null, "ComplianceStatus": null, "AssessorUser": {"BusinessUnit": null, "PrimaryRole": {"RoleCode": "Assessor", "Description": "Assessor", "Deleted": false}, "Client": null, "UserId": "414d94ea-dda3-4464-bc35-e4109267fa21", "AspNetUserId": "20F30EAB-E769-4D19-AF96-7478CDF50F67", "LoginEnabled": true, "IsExternal": false, "FullName": "<PERSON>", "FirstName": "<PERSON>", "LastName": "<PERSON>", "EmailAddress": "<EMAIL>", "Phone": null, "MobilePhone": null, "Deleted": false, "BusinessUnitId": null, "BusinessUnitName": null, "JobTitle": null, "CreatedOn": "2022-10-18T14:47:22.54+08:00", "CreatedByName": "<PERSON>", "CreatedBy": "00000000-0000-0000-0000-000000000000", "ModifiedOn": "2022-11-16T04:52:11.397+08:00", "ModifiedByName": "Thermarate Admin", "ModifiedBy": null, "ClientId": null, "PrimaryRoleCode": "Assessor", "PrimaryRoleDescription": "Assessor", "SignatureSVGImage": "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" width=\"283.465\" height=\"170\"><g transform=\"scale(0.23529411764705882)\"><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 20 19 c -0.03 0.07 -1.81 2.68 -2 4 c -0.42 2.95 -0.36 6.79 0 10 c 0.29 2.64 0.92 6.2 2 8 c 0.58 0.97 2.62 1.58 4 2 c 2.84 0.87 6.65 2.59 9 2 c 3.32 -0.83 7.81 -4.41 11 -7 c 1.91 -1.55 3.5 -3.84 5 -6 c 3.89 -5.59 7.85 -11.49 11 -17 c 0.64 -1.12 0.42 -2.83 1 -4 c 1.65 -3.31 5.91 -9.82 6 -10 c 0.04 -0.08 -2.28 2.56 -3 4 c -1.87 3.75 -3.47 7.81 -5 12 c -5 13.67 -9.65 26.67 -14 40 c -0.62 1.91 -0.29 4.16 -1 6 c -4.17 10.83 -9.78 21.61 -14 33 c -3.58 9.65 -6.38 19 -9 29 c -2.83 10.79 -4.31 21.25 -7 32 l -4 12\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 80 26 c 0.14 -0.14 5.14 -5.64 8 -8 c 2.74 -2.26 5.9 -4.67 9 -6 c 3.54 -1.52 8.31 -2.57 12 -3 c 1.52 -0.18 3.68 0.28 5 1 c 2.02 1.1 5.37 3.23 6 5 c 0.73 2.05 -0.13 6.27 -1 9 c -1.68 5.26 -4.38 10.62 -7 16 c -3.89 7.98 -7.38 15.82 -12 23 c -4.31 6.7 -9.75 12.75 -15 19 c -1.84 2.19 -3.8 4.22 -6 6 c -4.81 3.9 -10.57 6.85 -15 11 c -5.74 5.37 -10.45 12.2 -16 18 c -1.8 1.88 -3.86 3.72 -6 5 c -2.7 1.62 -5.9 3.01 -9 4 c -5.19 1.66 -11.63 4 -16 4 c -2.14 0 -4.66 -2.93 -7 -4 c -1.2 -0.55 -3.17 -0.27 -4 -1 c -1.45 -1.27 -2.99 -3.99 -4 -6 c -0.57 -1.15 -0.91 -2.67 -1 -4 c -0.24 -3.54 -0.52 -7.49 0 -11 c 0.78 -5.26 2.18 -11.13 4 -16 c 1.04 -2.78 3.01 -5.74 5 -8 c 2.84 -3.22 6.57 -6.1 10 -9 c 0.92 -0.78 1.96 -1.65 3 -2 c 1.75 -0.58 4.15 -1 6 -1 c 1.25 0 2.87 0.36 4 1 c 6.15 3.48 12.97 7.64 19 12 c 3.61 2.61 7.21 5.82 10 9 c 1.67 1.91 2.94 4.53 4 7 c 3.96 9.23 6.97 19.17 11 28 c 1.15 2.52 2.96 5.93 5 7 c 3.39 1.78 9.38 2.27 14 3 c 1.61 0.25 3.58 0.47 5 0 l 10 -5\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 95 26 c -0.05 0.19 -1.58 7.48 -3 11 c -4.92 12.16 -10.93 23.51 -16 36 c -4.52 11.14 -7.88 22.42 -12 33 c -0.42 1.09 -1.64 1.93 -2 3 c -1.54 4.62 -3.07 10.11 -4 15 l 0 6\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 99 94 c -0.09 0.3 -3.68 11.29 -5 17 c -0.67 2.92 -1.63 7.25 -1 9 c 0.38 1.04 3.38 1.68 5 2 c 1.51 0.3 3.61 0.46 5 0 c 2.23 -0.74 5.08 -2.38 7 -4 c 2.19 -1.86 4.43 -4.47 6 -7 c 2.66 -4.29 4.91 -9.2 7 -14 c 1.26 -2.91 1.71 -6.25 3 -9 c 0.98 -2.09 4.06 -6.17 4 -6 c -0.31 0.95 -8.68 24.58 -14 37 c -6.98 16.29 -22 47 -22 47\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 136 71 c 0 0.1 -0.41 4.22 0 6 c 0.52 2.26 1.53 5.67 3 7 c 1.64 1.49 5.74 3 8 3 c 2.02 0 6.77 -2.54 7 -3 c 0.15 -0.31 -3.76 -1.52 -5 -1 c -4.05 1.71 -11.35 5.12 -14 9 c -5 7.31 -8.41 19.26 -12 29 c -1.05 2.86 -2 6.6 -2 9 c 0 0.94 1.35 3.15 2 3 c 2.26 -0.52 7.95 -3.89 11 -6 c 0.88 -0.61 1.18 -2.18 2 -3 c 2.41 -2.41 5.87 -4.41 8 -7 c 2.34 -2.84 4.41 -6.52 6 -10 c 2.03 -4.43 5.12 -14.23 5 -14 c -0.2 0.41 -7.23 15.89 -10 24 c -1.53 4.46 -2.27 9.38 -3 14 c -0.25 1.61 -0.67 4.44 0 5 c 0.81 0.67 4.4 0.48 6 0 c 1.33 -0.4 2.86 -1.86 4 -3 c 1.46 -1.46 2.73 -3.23 4 -5 c 2.14 -3 4.47 -5.82 6 -9 c 2.73 -5.67 6.21 -17.89 7 -18 c 0.7 -0.1 -0.25 11.43 0 17 c 0.08 1.67 0.48 3.45 1 5 c 0.45 1.36 1.12 3.12 2 4 c 0.88 0.88 2.7 1.71 4 2 c 1.46 0.32 3.49 0.35 5 0 c 2.58 -0.6 6.31 -1.24 8 -3 l 16 -22\"/></g></svg>", "SignatureBase64Image": null, "UserName": "<PERSON>", "Password": null, "IsGoogleLinked": false, "IsMicrosoftLinked": false}, "BushfireAttackLevel": null, "Priority": null, "AssessmentProjectDetail": {"Assessment": null, "Creator": {"BusinessUnit": null, "PrimaryRole": {"RoleCode": "Administrator", "Description": "Administrator", "Deleted": false}, "Client": null, "UserId": "0667ef95-d47e-47f0-9670-e56d4e396203", "AspNetUserId": "C65BA6BD-2D80-4BEE-B213-6ACFA0016C15", "LoginEnabled": true, "IsExternal": false, "FullName": "Support Support", "FirstName": "Support", "LastName": "Support", "EmailAddress": "<EMAIL>", "Phone": null, "MobilePhone": null, "Deleted": false, "BusinessUnitId": null, "BusinessUnitName": null, "JobTitle": "Support", "CreatedOn": "2022-09-11T12:42:14.847+08:00", "CreatedByName": "Admin Admin", "CreatedBy": "00000000-0000-0000-0000-000000000000", "ModifiedOn": "2024-03-12T08:18:52.507+08:00", "ModifiedByName": "Support Support", "ModifiedBy": null, "ClientId": null, "PrimaryRoleCode": "Administrator", "PrimaryRoleDescription": "Administrator", "SignatureSVGImage": null, "SignatureBase64Image": null, "UserName": "<EMAIL>", "Password": null, "IsGoogleLinked": false, "IsMicrosoftLinked": false}, "ClientAssignee": {"BusinessUnit": null, "PrimaryRole": null, "Client": {"ClientDefault": {"AssessorUser": {"UserId": "414d94ea-dda3-4464-bc35-e4109267fa21", "AspNetUserId": "20F30EAB-E769-4D19-AF96-7478CDF50F67", "LoginEnabled": true, "IsExternal": false, "FullName": "<PERSON>", "FirstName": "<PERSON>", "LastName": "<PERSON>", "EmailAddress": "<EMAIL>", "Phone": null, "MobilePhone": null, "Deleted": false, "BusinessUnitId": null, "BusinessUnitName": null, "JobTitle": null, "CreatedOn": "2022-10-18T14:47:22.54+08:00", "CreatedByName": "<PERSON>", "CreatedBy": "00000000-0000-0000-0000-000000000000", "ModifiedOn": "2022-11-16T04:52:11.397+08:00", "ModifiedByName": "Thermarate Admin", "ModifiedBy": null, "ClientId": null, "PrimaryRoleCode": "Assessor", "PrimaryRoleDescription": "Assessor", "SignatureSVGImage": "<svg xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\" width=\"283.465\" height=\"170\"><g transform=\"scale(0.23529411764705882)\"><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 20 19 c -0.03 0.07 -1.81 2.68 -2 4 c -0.42 2.95 -0.36 6.79 0 10 c 0.29 2.64 0.92 6.2 2 8 c 0.58 0.97 2.62 1.58 4 2 c 2.84 0.87 6.65 2.59 9 2 c 3.32 -0.83 7.81 -4.41 11 -7 c 1.91 -1.55 3.5 -3.84 5 -6 c 3.89 -5.59 7.85 -11.49 11 -17 c 0.64 -1.12 0.42 -2.83 1 -4 c 1.65 -3.31 5.91 -9.82 6 -10 c 0.04 -0.08 -2.28 2.56 -3 4 c -1.87 3.75 -3.47 7.81 -5 12 c -5 13.67 -9.65 26.67 -14 40 c -0.62 1.91 -0.29 4.16 -1 6 c -4.17 10.83 -9.78 21.61 -14 33 c -3.58 9.65 -6.38 19 -9 29 c -2.83 10.79 -4.31 21.25 -7 32 l -4 12\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 80 26 c 0.14 -0.14 5.14 -5.64 8 -8 c 2.74 -2.26 5.9 -4.67 9 -6 c 3.54 -1.52 8.31 -2.57 12 -3 c 1.52 -0.18 3.68 0.28 5 1 c 2.02 1.1 5.37 3.23 6 5 c 0.73 2.05 -0.13 6.27 -1 9 c -1.68 5.26 -4.38 10.62 -7 16 c -3.89 7.98 -7.38 15.82 -12 23 c -4.31 6.7 -9.75 12.75 -15 19 c -1.84 2.19 -3.8 4.22 -6 6 c -4.81 3.9 -10.57 6.85 -15 11 c -5.74 5.37 -10.45 12.2 -16 18 c -1.8 1.88 -3.86 3.72 -6 5 c -2.7 1.62 -5.9 3.01 -9 4 c -5.19 1.66 -11.63 4 -16 4 c -2.14 0 -4.66 -2.93 -7 -4 c -1.2 -0.55 -3.17 -0.27 -4 -1 c -1.45 -1.27 -2.99 -3.99 -4 -6 c -0.57 -1.15 -0.91 -2.67 -1 -4 c -0.24 -3.54 -0.52 -7.49 0 -11 c 0.78 -5.26 2.18 -11.13 4 -16 c 1.04 -2.78 3.01 -5.74 5 -8 c 2.84 -3.22 6.57 -6.1 10 -9 c 0.92 -0.78 1.96 -1.65 3 -2 c 1.75 -0.58 4.15 -1 6 -1 c 1.25 0 2.87 0.36 4 1 c 6.15 3.48 12.97 7.64 19 12 c 3.61 2.61 7.21 5.82 10 9 c 1.67 1.91 2.94 4.53 4 7 c 3.96 9.23 6.97 19.17 11 28 c 1.15 2.52 2.96 5.93 5 7 c 3.39 1.78 9.38 2.27 14 3 c 1.61 0.25 3.58 0.47 5 0 l 10 -5\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 95 26 c -0.05 0.19 -1.58 7.48 -3 11 c -4.92 12.16 -10.93 23.51 -16 36 c -4.52 11.14 -7.88 22.42 -12 33 c -0.42 1.09 -1.64 1.93 -2 3 c -1.54 4.62 -3.07 10.11 -4 15 l 0 6\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 99 94 c -0.09 0.3 -3.68 11.29 -5 17 c -0.67 2.92 -1.63 7.25 -1 9 c 0.38 1.04 3.38 1.68 5 2 c 1.51 0.3 3.61 0.46 5 0 c 2.23 -0.74 5.08 -2.38 7 -4 c 2.19 -1.86 4.43 -4.47 6 -7 c 2.66 -4.29 4.91 -9.2 7 -14 c 1.26 -2.91 1.71 -6.25 3 -9 c 0.98 -2.09 4.06 -6.17 4 -6 c -0.31 0.95 -8.68 24.58 -14 37 c -6.98 16.29 -22 47 -22 47\"/><path fill=\"none\" stroke=\"#000000\" stroke-width=\"5.294117647058823\" stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M 136 71 c 0 0.1 -0.41 4.22 0 6 c 0.52 2.26 1.53 5.67 3 7 c 1.64 1.49 5.74 3 8 3 c 2.02 0 6.77 -2.54 7 -3 c 0.15 -0.31 -3.76 -1.52 -5 -1 c -4.05 1.71 -11.35 5.12 -14 9 c -5 7.31 -8.41 19.26 -12 29 c -1.05 2.86 -2 6.6 -2 9 c 0 0.94 1.35 3.15 2 3 c 2.26 -0.52 7.95 -3.89 11 -6 c 0.88 -0.61 1.18 -2.18 2 -3 c 2.41 -2.41 5.87 -4.41 8 -7 c 2.34 -2.84 4.41 -6.52 6 -10 c 2.03 -4.43 5.12 -14.23 5 -14 c -0.2 0.41 -7.23 15.89 -10 24 c -1.53 4.46 -2.27 9.38 -3 14 c -0.25 1.61 -0.67 4.44 0 5 c 0.81 0.67 4.4 0.48 6 0 c 1.33 -0.4 2.86 -1.86 4 -3 c 1.46 -1.46 2.73 -3.23 4 -5 c 2.14 -3 4.47 -5.82 6 -9 c 2.73 -5.67 6.21 -17.89 7 -18 c 0.7 -0.1 -0.25 11.43 0 17 c 0.08 1.67 0.48 3.45 1 5 c 0.45 1.36 1.12 3.12 2 4 c 0.88 0.88 2.7 1.71 4 2 c 1.46 0.32 3.49 0.35 5 0 c 2.58 -0.6 6.31 -1.24 8 -3 l 16 -22\"/></g></svg>", "SignatureBase64Image": null, "UserName": "<PERSON>", "Password": null, "IsGoogleLinked": false, "IsMicrosoftLinked": false}, "ClientDefaultId": "38aa1885-a2b0-baa4-30c6-3a06fbad0c32", "ClientId": "00000000-0000-0000-0000-000000000000", "CreatedOn": "2022-10-18T13:10:36.85+08:00", "CreatedByName": "<PERSON>", "ModifiedOn": "2022-10-24T18:29:38.76+08:00", "ModifiedByName": "Thermarate Admin", "Deleted": false, "PreliminaryComplianceMethodCode": "CMPerfSolutionDTS", "AssessmentSoftwareCode": "ASEnergyPlus", "MinHouseEnergyRating": null, "PriorityCode": "NORMAL", "CertificationId": "28b2b2fb-d5f2-455b-90e6-24275892a31b", "AssessorUserId": "414d94ea-dda3-4464-bc35-e4109267fa21", "BuildingExposureCode": "BESuburban", "PurchaseOrderCode": "NotRequired", "IncludeDrawingsInReport": true, "StampDrawings": true, "EmailAssessorNewJob": null, "EmailAssessorRecertification": null, "EmailAssessorCopyAssessment": null, "NominatedBuildingSurveyorId": "b9ca3679-5ad6-4a25-9afe-f79c5131cfdb", "WorksDescription": {"WorksDescriptionCode": "WDNewBuild", "Description": "New Dwelling", "CreatedOn": "2021-03-22T12:00:00.1+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "SortOrder": 1, "Deleted": false}, "ProposedConstructionTemplateId": "BLANK_TEMPLATE", "ReferenceConstructionTemplateId": "BLANK_TEMPLATE", "ProposedOpeningTemplateId": "BLANK_TEMPLATE", "ReferenceOpeningTemplateId": "BLANK_TEMPLATE", "ProposedServicesTemplateId": "2353ad7a-604a-78fb-b740-3a02654e66c8", "ReferenceServicesTemplateId": "2353ad7a-604a-78fb-b740-3a02654e66c8", "BuildingZonesTemplateId": "BLANK_TEMPLATE", "ReferenceBuildingZonesTemplateId": "BLANK_TEMPLATE", "AssessorNotesNotApplicable": true, "AssessorNotes": null, "ProjectDescription": null, "NccBuildingClassification": null, "SpaceHeatingServiceTypeCode": null, "SpaceHeatingGems2019Rating": null, "SpaceCoolingServiceTypeCode": null, "SpaceCoolingGems2019Rating": null, "WaterHeatingServiceTypeCode": null, "WaterHeatingGems2019Rating": null, "SwimmingPoolExists": null, "SwimmingPoolVolume": null, "SwimmingPoolGems2019Rating": null, "SpaExists": null, "SpaVolume": null, "SpaGems2019Rating": null, "PhotovoltaicExists": null, "PhotovoltaicCapacity": null}, "UsersCount": 2, "JobsCount": 438, "JobsActiveCount": 0, "EnergyLabsIsActive": false, "ProjectsActiveCount": 0, "HomeDesignsActiveCount": 0, "HomeDesignVariationsActiveCount": 0, "ClientId": "f1aeb4db-e325-ba98-4ac9-3a06fbad0c32", "ClientName": "Affordable Living Homes", "CreatedOn": "2022-10-18T13:10:36.897+08:00", "CreatedByName": "<PERSON>", "ModifiedOn": "2022-10-24T18:29:38.76+08:00", "ModifiedByName": "Thermarate Admin", "Deleted": false, "DefaultClientAssigneeUserId": "b4c8477c-ff2f-406c-bc53-7a46c1429bdc", "AccountsFirstName": "<PERSON>", "AccountsLastName": "<PERSON><PERSON><PERSON>", "AccountsPhone": "(08) 6200 0272", "AccountsEmail": "<EMAIL>", "AccountsNote": null, "AccountsSameAsContact": false, "HasClientPortalAccess": true, "IsFavourite": false, "ClientOptions": {"AvailableBuildingSurveyorIds": ["b9ca3679-5ad6-4a25-9afe-f79c5131cfdb", "2502eb9c-cc6d-4711-b13c-a57bec57e9ab"], "AvailableComplianceMethodCodes": ["CMHouseEnergyRating", "CMPerfSolutionDTS"], "AvailableHouseEnergyRatings": ["6.0", "6.5", "7.0", "7.5", "8.0", "8.5", "9.0", "9.5", "10.0"], "AvailableCertifications": ["28b2b2fb-d5f2-455b-90e6-24275892a31b"], "AvailableWorksDescriptions": ["WDNewBuild"], "HeatingAndCoolingRulesetCode": "Inherit", "IncludeDrawingsInReport": false, "StampDrawings": false, "ComplianceCostEnabled": false, "Ncc2022ThermPerfEnabled": false, "Ncc2022WohEnabled": false, "AnalyticsEnabled": false}, "ReportSettings": {"SpecificationSummaryReports": {"Baseline": {"ShowOnClientPortal": true, "AllowDownloadOnClientPortal": false}, "ComplianceOption": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}}, "ComplianceReports": {"PreliminaryReport": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}, "EnergyEfficiencyPbdb": {"ShowOnClientPortal": true, "AllowDownloadOnClientPortal": true}, "EnergyEfficiencyComplianceReport": {"ShowOnClientPortal": true, "AllowDownloadOnClientPortal": true}, "LightAndVentilationReport": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}, "SoftwareReport": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}, "Specification44Report": {"ShowOnClientPortal": false, "AllowDownloadOnClientPortal": false}}}, "ClientCostItems": []}, "UserId": "b4c8477c-ff2f-406c-bc53-7a46c1429bdc", "AspNetUserId": "E4FF1303-5BC1-4059-A0C4-750EB9B348CF", "LoginEnabled": true, "IsExternal": true, "FullName": "<PERSON><PERSON>", "FirstName": "<PERSON><PERSON>", "LastName": "<PERSON>", "EmailAddress": "<EMAIL>", "Phone": "(08) 6200 0272", "MobilePhone": null, "Deleted": false, "BusinessUnitId": null, "BusinessUnitName": null, "JobTitle": null, "CreatedOn": "2022-10-18T13:10:37.68+08:00", "CreatedByName": "<PERSON>", "CreatedBy": "00000000-0000-0000-0000-000000000000", "ModifiedOn": "2022-10-24T13:26:27.247+08:00", "ModifiedByName": "Thermarate Admin", "ModifiedBy": null, "ClientId": "f1aeb4db-e325-ba98-4ac9-3a06fbad0c32", "PrimaryRoleCode": null, "PrimaryRoleDescription": null, "SignatureSVGImage": null, "SignatureBase64Image": null, "UserName": "<EMAIL>", "Password": null, "IsGoogleLinked": false, "IsMicrosoftLinked": false}, "LotType": null, "MapImageFile": {"FileId": "9bab8c3f-e39e-4321-92b5-6a7a3fb9963d", "DisplayName": "map_64ff7340-626d-d765-a9c9-3a1b0405bef2_0a06fc0c-7469-4b74-a2d9-bdb1181ad4981835849919.png", "FileName": "map_64ff7340-626d-d765-a9c9-3a1b0405bef2_0a06fc0c-7469-4b74-a2d9-bdb1181ad4981835849919.png", "FolderName": "C:\\repo\\thermarate-assessment\\Thermarate-Assessment\\Uploads\\", "URL": "http://redisstest-thermarate.s3-ap-southeast-2.amazonaws.com/maps/map_64ff7340-626d-d765-a9c9-3a1b0405bef2_0a06fc0c-7469-4b74-a2d9-bdb1181ad4981835849919.png", "CreatedOn": "2025-07-10T09:06:14.256+08:00", "CreatedByName": "Support Support", "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "VersionNo": 1, "JobId": "64ff7340-626d-d765-a9c9-3a1b0405bef2", "AssessmentId": "0a06fc0c-7469-4b74-a2d9-bdb1181ad498", "PageCount": 1, "SortOrder": null, "OriginalFileUrl": null, "SizeInBytes": null, "ComplianceOptionId": null, "ShowOnClientPortal": null, "AllowDownloadOnClientPortal": null, "Category": "Images", "Classification": "Map Image"}, "MapImageFileId": "9bab8c3f-e39e-4321-92b5-6a7a3fb9963d", "SiteMapImageFile": {"FileId": "f679403c-2aec-43fd-b7e0-366d5353d84d", "DisplayName": "map_64ff7340-626d-d765-a9c9-3a1b0405bef2_0a06fc0c-7469-4b74-a2d9-bdb1181ad498355991615.png", "FileName": "map_64ff7340-626d-d765-a9c9-3a1b0405bef2_0a06fc0c-7469-4b74-a2d9-bdb1181ad498355991615.png", "FolderName": "C:\\repo\\thermarate-assessment\\Thermarate-Assessment\\Uploads\\", "URL": "http://redisstest-thermarate.s3-ap-southeast-2.amazonaws.com/maps/map_64ff7340-626d-d765-a9c9-3a1b0405bef2_0a06fc0c-7469-4b74-a2d9-bdb1181ad498355991615.png", "CreatedOn": "2025-07-10T09:06:18.191+08:00", "CreatedByName": "Support Support", "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "VersionNo": 1, "JobId": "64ff7340-626d-d765-a9c9-3a1b0405bef2", "AssessmentId": "0a06fc0c-7469-4b74-a2d9-bdb1181ad498", "PageCount": 1, "SortOrder": null, "OriginalFileUrl": null, "SizeInBytes": null, "ComplianceOptionId": null, "ShowOnClientPortal": null, "AllowDownloadOnClientPortal": null, "Category": "Images", "Classification": "Map Image"}, "SiteMapImageFileId": "f679403c-2aec-43fd-b7e0-366d5353d84d", "ClimateChartData": null, "ClimateChartDataJson": null, "NominatedBuildingSurveyor": null, "BoundaryGeometry": [[-32.24190411219849, 148.25101971554886], [-32.24193368438225, 148.2513381793118], [-32.24167535238254, 148.25138228150462], [-32.24163634113676, 148.25106543273995], [-32.24190411219849, 148.25101971554886]], "AssessmentId": "0a06fc0c-7469-4b74-a2d9-bdb1181ad498", "Latitude": -32.24178698, "Longitude": 148.251201, "LotTypeCode": null, "Prefix": null, "StrataLotNumber": null, "SurveyStrataLotNumber": null, "OriginalLotNumber": null, "LotNumber": null, "DepositedPlanNumber": "P81278", "PlanType": "DP", "CertificateOfTitle": null, "ParcelArea": 889.37, "BoundarySides": 4, "Volume": null, "Folio": null, "HouseNumber": "123", "StreetName": "A'beckett Street", "StreetType": null, "Suburb": "Narromine", "StateCode": "NSW", "Postcode": "2821", "LocalGovernmentAuthority": "Narromine Shire Council", "LocalGovernmentAuthorityShort": "Narromine", "CreatedOn": "2025-07-10T09:06:19.36046+08:00", "CreatedByName": "Support Support", "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "ClientJobNumber": "1212123", "ProjectOwner": "2324234", "OrderDate": "2025-07-10T08:50:54.478+08:00", "AssessmentVersion": 1.0, "FullAddress": "123 A'beckett Street, Narromine NSW 2821", "NominatedBuildingSurveyorId": "b9ca3679-5ad6-4a25-9afe-f79c5131cfdb", "CompletedDate": null, "UseCustomAddress": null, "CustomDisplayAddress": null, "IsRecertified": false, "Address": null, "IsManual": false, "CoordinatesAreConfirmed": true, "LotShape": null, "NorthSideFacing": null, "FacingDescription": null, "FacingWithinXMetres": null, "CornerBlock": "No", "LotDescription": null, "RearLaneway": null, "RuralLot": null, "LotWidth": null, "LotLength": null, "NarrowLot": null, "NarrowLotIsManuallyOverridden": null, "UseCustomClientName": null, "CustomClientName": null, "ComplianceCost": null}, "IncludeMapInExportedPDF": true, "AssessmentId": "0a06fc0c-7469-4b74-a2d9-bdb1181ad498", "JobId": "7acf15e6-12f2-df4b-5b88-3a1b040b51ac", "JobClientJobNumber": null, "StatusCode": "ADraft", "StatusDescription": null, "AssessmentSoftwareCode": null, "AssessmentSoftwareDescription": null, "AssessmentSoftwareOther": null, "NCCClimateZoneCode": "NCC4", "NCCClimateZoneDescription": null, "NatHERSClimateZoneCode": "Nat48", "NatHERSClimateZoneDescription": null, "BuildingExposureCode": "BESuburban", "BuildingExposureDescription": null, "PerformanceRequirementP261Code": null, "PerformanceRequirementP262Code": "P262Satisfied", "PerformanceRequirementP262Description": null, "ComplianceStatusCode": "CSNotAchieved", "ComplianceStatusDescription": null, "WorksDescriptionCode": null, "WorksDescription": {"WorksDescriptionCode": "WDNewBuild", "Description": "New Dwelling", "CreatedOn": "2021-03-22T12:00:00.1+08:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "SortOrder": 1, "Deleted": false}, "CertificateNumber": null, "CerficateDate": null, "LightAndVentilationCertificateNumber": null, "AssessorUserId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedByName": null, "ModifiedOn": null, "ModifiedByName": null, "Deleted": false, "BushfireAttackLevelCode": "BAL-NotApp", "PriorityCode": "NORMAL", "PriorityDescription": null, "IsBushFireProne": false, "BushFireProneUnknown": true, "AssessorNotesNotApplicable": true, "CertificateDateOverride": null, "Notes": null, "AssessorNotes": null, "RecertificationDesignChanges": null, "ComfortMetrics": null}